#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析A03设备操作类型统计
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_operations():
    """分析操作类型统计"""
    
    # 读取数据
    df = pd.read_csv('extracted_content_2.csv')
    
    print("数据基本信息:")
    print(f"总记录数: {len(df)}")
    print(f"时间范围: {df['cc_time'].min()} 到 {df['cc_time'].max()}")
    print()
    
    # 统计操作类型
    operation_counts = df['cc_work_procedure'].value_counts()
    
    print("操作类型统计:")
    print("=" * 50)
    for operation, count in operation_counts.items():
        percentage = (count / len(df)) * 100
        print(f"{operation}: {count} 次 ({percentage:.1f}%)")
    
    print()
    print("详细统计:")
    print("=" * 50)
    total_records = len(df)
    
    # 各种操作的详细统计
    operations = {
        '投入料筒': 0,
        '料筒复投': 0,
        '提出料筒': 0,
        '一键复投': 0,
        '一键平仓': 0,
        '一键开仓': 0,
        '手动下单': 0,
        '手动平仓': 0,
        '其他': 0
    }
    
    for operation in df['cc_work_procedure']:
        if operation in operations:
            operations[operation] += 1
        else:
            operations['其他'] += 1
    
    for op, count in operations.items():
        if count > 0:
            percentage = (count / total_records) * 100
            print(f"{op}: {count} 次 ({percentage:.1f}%)")
    
    # 创建可视化图表
    plt.figure(figsize=(12, 8))
    
    # 饼图
    plt.subplot(2, 2, 1)
    valid_operations = {k: v for k, v in operations.items() if v > 0}
    plt.pie(valid_operations.values(), labels=valid_operations.keys(), autopct='%1.1f%%')
    plt.title('操作类型分布')
    
    # 柱状图
    plt.subplot(2, 2, 2)
    plt.bar(valid_operations.keys(), valid_operations.values())
    plt.title('操作类型数量')
    plt.xticks(rotation=45)
    plt.ylabel('次数')
    
    # 时间序列分析
    plt.subplot(2, 1, 2)
    df['cc_time'] = pd.to_datetime(df['cc_time'])
    df['hour'] = df['cc_time'].dt.hour
    hourly_counts = df.groupby('hour').size()
    plt.plot(hourly_counts.index, hourly_counts.values, marker='o')
    plt.title('24小时操作分布')
    plt.xlabel('小时')
    plt.ylabel('操作次数')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('operation_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 按时间段分析
    print("\n按小时统计操作频率:")
    print("=" * 50)
    for hour in sorted(hourly_counts.index):
        print(f"{hour:02d}:00-{hour:02d}:59: {hourly_counts[hour]} 次")

if __name__ == "__main__":
    analyze_operations()
