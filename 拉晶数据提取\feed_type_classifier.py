#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
首投/复投分类器模块
Feed Type Classifier Module

功能：
1. 基于cc_feed_number字段判断首投/复投类型
2. 提供数据筛选功能
3. 支持批量分类和统计

作者：AI Assistant
日期：2025-01-07
"""

import pandas as pd
import numpy as np
import logging
from typing import Union, List, Dict, Tuple, Optional


class FeedTypeClassifier:
    """首投/复投分类器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化分类器
        
        Args:
            logger: 日志记录器，如果为None则创建新的
        """
        self.logger = logger or self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.FeedTypeClassifier")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def classify_feed_type(self, feed_number: Union[int, float, str]) -> str:
        """
        根据cc_feed_number值判断首投/复投类型

        Args:
            feed_number: cc_feed_number字段的值

        Returns:
            str: "首投" 或 "复投"
        """
        try:
            # 处理空值或无效值
            if pd.isna(feed_number):
                self.logger.warning(f"cc_feed_number值为空: {feed_number}")
                return "复投"  # 默认为复投

            # 转换为数值进行判断
            try:
                # 尝试转换为数值
                feed_value = float(feed_number)
                # 判断是否等于1
                if feed_value == 1.0:
                    return "首投"
                else:
                    return "复投"
            except (ValueError, TypeError):
                # 如果无法转换为数值，尝试字符串比较
                feed_str = str(feed_number).strip()
                if feed_str == "1" or feed_str == "1.0":
                    return "首投"
                else:
                    return "复投"

        except Exception as e:
            self.logger.error(f"分类cc_feed_number时出错: {feed_number}, 错误: {e}")
            return "复投"  # 出错时默认为复投
    
    def classify_dataframe(self, df: pd.DataFrame, 
                          feed_number_column: str = 'cc_feed_number') -> pd.DataFrame:
        """
        为DataFrame添加首投/复投分类列
        
        Args:
            df: 数据框
            feed_number_column: cc_feed_number列名
            
        Returns:
            pd.DataFrame: 添加了feed_type列的数据框
        """
        try:
            if feed_number_column not in df.columns:
                self.logger.error(f"数据框中未找到列: {feed_number_column}")
                # 添加默认列
                df['feed_type'] = "复投"
                return df
            
            # 应用分类函数
            df['feed_type'] = df[feed_number_column].apply(self.classify_feed_type)
            
            # 记录分类统计
            type_counts = df['feed_type'].value_counts()
            self.logger.info(f"分类完成 - 首投: {type_counts.get('首投', 0)}条, "
                           f"复投: {type_counts.get('复投', 0)}条")
            
            return df
            
        except Exception as e:
            self.logger.error(f"DataFrame分类时出错: {e}")
            # 添加默认列
            df['feed_type'] = "复投"
            return df
    
    def filter_by_feed_type(self, df: pd.DataFrame, 
                           feed_type: str,
                           feed_number_column: str = 'cc_feed_number') -> pd.DataFrame:
        """
        根据首投/复投类型筛选数据
        
        Args:
            df: 数据框
            feed_type: 筛选类型 ("首投", "复投", "全部")
            feed_number_column: cc_feed_number列名
            
        Returns:
            pd.DataFrame: 筛选后的数据框
        """
        try:
            if feed_type == "全部":
                return df
            
            # 如果没有feed_type列，先进行分类
            if 'feed_type' not in df.columns:
                df = self.classify_dataframe(df, feed_number_column)
            
            # 筛选数据
            filtered_df = df[df['feed_type'] == feed_type].copy()
            
            self.logger.info(f"筛选完成 - 类型: {feed_type}, "
                           f"筛选前: {len(df)}条, 筛选后: {len(filtered_df)}条")
            
            return filtered_df
            
        except Exception as e:
            self.logger.error(f"数据筛选时出错: {e}")
            return df
    
    def get_feed_type_statistics(self, df: pd.DataFrame,
                                feed_number_column: str = 'cc_feed_number') -> Dict:
        """
        获取首投/复投统计信息
        
        Args:
            df: 数据框
            feed_number_column: cc_feed_number列名
            
        Returns:
            Dict: 统计信息字典
        """
        try:
            # 如果没有feed_type列，先进行分类
            if 'feed_type' not in df.columns:
                df = self.classify_dataframe(df, feed_number_column)
            
            type_counts = df['feed_type'].value_counts()
            total_count = len(df)
            
            stats = {
                "总记录数": total_count,
                "首投记录数": type_counts.get('首投', 0),
                "复投记录数": type_counts.get('复投', 0),
                "首投比例": (type_counts.get('首投', 0) / total_count * 100) if total_count > 0 else 0,
                "复投比例": (type_counts.get('复投', 0) / total_count * 100) if total_count > 0 else 0
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"统计计算时出错: {e}")
            return {
                "总记录数": len(df),
                "首投记录数": 0,
                "复投记录数": 0,
                "首投比例": 0,
                "复投比例": 0
            }
    
    def analyze_feed_number_patterns(self, df: pd.DataFrame,
                                   feed_number_column: str = 'cc_feed_number') -> Dict:
        """
        分析cc_feed_number字段的模式
        
        Args:
            df: 数据框
            feed_number_column: cc_feed_number列名
            
        Returns:
            Dict: 模式分析结果
        """
        try:
            if feed_number_column not in df.columns:
                return {"error": f"未找到列: {feed_number_column}"}
            
            # 获取唯一值
            unique_values = df[feed_number_column].unique()
            unique_values = [v for v in unique_values if not pd.isna(v)]
            
            # 分析等于1的值
            equals_1 = []
            not_equals_1 = []

            for value in unique_values:
                try:
                    if float(value) == 1.0:
                        equals_1.append(value)
                    else:
                        not_equals_1.append(value)
                except (ValueError, TypeError):
                    # 无法转换为数值的值归类为非首投
                    not_equals_1.append(value)
            
            # 统计每个值的出现次数
            value_counts = df[feed_number_column].value_counts()
            
            analysis = {
                "唯一值总数": len(unique_values),
                "等于1的值": sorted(equals_1),
                "不等于1的值": sorted(not_equals_1),
                "等于1的值数量": len(equals_1),
                "不等于1的值数量": len(not_equals_1),
                "值分布": dict(value_counts.head(10))  # 前10个最常见的值
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"模式分析时出错: {e}")
            return {"error": str(e)}


def test_feed_type_classifier():
    """测试首投/复投分类器"""
    print("开始测试首投/复投分类器...")
    
    # 创建分类器
    classifier = FeedTypeClassifier()
    
    # 测试单个值分类
    test_values = [0, 1, 2, 7, 8, 10, 11, 21, 31, 41, 51, 61, 71, 81, 91, 101, 111, 121, 1.0, "1", "1.0", "11"]
    
    print("\n单个值分类测试:")
    print("-" * 40)
    for value in test_values:
        result = classifier.classify_feed_type(value)
        print(f"cc_feed_number = {value} -> {result}")
    
    # 测试DataFrame分类
    print("\nDataFrame分类测试:")
    print("-" * 40)
    
    test_data = {
        'cc_feed_number': [0, 1, 2, 7, 8, 10, 11, 21],
        'other_column': ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
    }
    
    df = pd.DataFrame(test_data)
    print("原始数据:")
    print(df)
    
    # 添加分类
    df_classified = classifier.classify_dataframe(df)
    print("\n分类后数据:")
    print(df_classified)
    
    # 获取统计信息
    stats = classifier.get_feed_type_statistics(df_classified)
    print("\n统计信息:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    # 测试筛选功能
    print("\n筛选测试:")
    print("-" * 40)
    
    first_feed = classifier.filter_by_feed_type(df_classified, "首投")
    print(f"首投数据 ({len(first_feed)}条):")
    print(first_feed)
    
    repeat_feed = classifier.filter_by_feed_type(df_classified, "复投")
    print(f"\n复投数据 ({len(repeat_feed)}条):")
    print(repeat_feed)
    
    print("\n测试完成!")


if __name__ == "__main__":
    test_feed_type_classifier()
