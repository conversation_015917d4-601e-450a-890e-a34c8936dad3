#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拉晶设备运行数据分析脚本 - 支持首投/复投筛选
Crystal Equipment Operation Data Analysis Script with Feed Type Filter

功能：
1. 支持交互式选择首投/复投筛选
2. 生成筛选后的分析结果
3. 提供详细的统计报告

作者：AI Assistant
日期：2025-01-07
"""

import os
import sys
import json
from crystal_data_analyzer import CrystalDataAnalyzer


def get_user_choice():
    """获取用户的筛选选择"""
    print("\n" + "="*50)
    print("拉晶设备运行数据分析 - 首投/复投筛选")
    print("="*50)
    print("\n请选择要分析的数据类型：")
    print("1. 全部数据（首投+复投）")
    print("2. 仅首投数据")
    print("3. 仅复投数据")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            if choice == "1":
                return "全部"
            elif choice == "2":
                return "首投"
            elif choice == "3":
                return "复投"
            elif choice == "4":
                print("退出程序。")
                sys.exit(0)
            else:
                print("无效选择，请输入1-4之间的数字。")
        except KeyboardInterrupt:
            print("\n\n程序被用户中断。")
            sys.exit(0)
        except Exception as e:
            print(f"输入错误: {e}")


def create_config_with_filter(filter_type):
    """创建带有筛选配置的配置文件"""
    config = {
        "input_directory": ".",
        "output_directory": "output_results",
        "time_format": "%Y-%m-%d %H:%M:%S",
        "preheating_duration_minutes": 10,
        "required_columns": [
            "cc_time",
            "cc_work_procedure",
            "cc_residue_weight",
            "cc_main_heating_display",
            "cc_vice_heating_set",
            "cc_ccd_liquid_temperature"
        ],
        "target_procedure": "预调温",
        "crystal_seeding_keyword": "引晶",
        "encoding": "utf-8",
        "feed_type_analysis": {
            "enabled": True,
            "feed_number_column": "cc_feed_number",
            "filter_type": filter_type
        }
    }
    
    config_file = f"config_feed_filter_{filter_type}.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    
    return config_file


def run_analysis_with_filter():
    """运行带筛选的分析"""
    try:
        # 获取用户选择
        filter_type = get_user_choice()
        
        print(f"\n开始分析 - 筛选类型: {filter_type}")
        print("-" * 40)
        
        # 创建配置文件
        config_file = create_config_with_filter(filter_type)
        print(f"已创建配置文件: {config_file}")
        
        # 创建分析器并运行分析
        analyzer = CrystalDataAnalyzer(config_file)
        analyzer.run_analysis()
        
        # 显示结果摘要
        print("\n" + "="*50)
        print("分析完成！")
        print("="*50)
        
        output_dir = analyzer.config['output_directory']
        print(f"\n结果文件位置: {output_dir}/")
        print("主要输出文件:")
        print(f"  - all_folders_summary.csv (全部数据汇总)")
        
        if filter_type != "全部":
            print(f"  - filtered_{filter_type}_summary.csv (筛选后数据)")
        
        print(f"  - analysis_report.txt (统计报告)")
        print(f"  - analysis_report.json (详细统计)")
        
        # 显示首投/复投统计
        try:
            report_file = os.path.join(output_dir, "analysis_report.json")
            if os.path.exists(report_file):
                with open(report_file, 'r', encoding='utf-8') as f:
                    report = json.load(f)
                
                feed_stats = report.get('首投/复投统计', {})
                if feed_stats:
                    print(f"\n首投/复投统计:")
                    print(f"  - 首投文件: {feed_stats.get('首投文件数', 0)}个 ({feed_stats.get('首投文件比例(%)', 0):.1f}%)")
                    print(f"  - 复投文件: {feed_stats.get('复投文件数', 0)}个 ({feed_stats.get('复投文件比例(%)', 0):.1f}%)")
                    print(f"  - 未知类型: {feed_stats.get('未知类型文件数', 0)}个")
        except Exception as e:
            print(f"读取统计信息时出错: {e}")
        
        # 清理临时配置文件
        try:
            os.remove(config_file)
            print(f"\n已清理临时配置文件: {config_file}")
        except:
            pass
        
        print(f"\n分析完成！请查看 {output_dir} 文件夹中的结果文件。")
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。")
        sys.exit(0)
    except Exception as e:
        print(f"\n分析过程中出错: {e}")
        print("请检查:")
        print("1. 数据文件是否存在")
        print("2. 数据文件格式是否正确")
        print("3. 是否有足够的磁盘空间")
        print("4. 是否有文件写入权限")
        sys.exit(1)


def show_help():
    """显示帮助信息"""
    print("拉晶设备运行数据分析脚本 - 首投/复投筛选版")
    print("="*50)
    print("\n功能说明:")
    print("1. 自动分析所有设备文件夹中的CSV数据")
    print("2. 根据cc_feed_number字段判断首投/复投类型")
    print("3. 支持按类型筛选分析结果")
    print("4. 生成详细的统计报告")
    print("\n判断逻辑:")
    print("- 如果文件中cc_feed_number字段有任何记录等于1，则标记为'首投'")
    print("- 否则标记为'复投'")
    print("\n使用方法:")
    print("python run_analysis_with_feed_filter.py")
    print("\n输出文件:")
    print("- all_folders_summary.csv: 全部数据汇总")
    print("- filtered_[类型]_summary.csv: 筛选后数据（如果选择了筛选）")
    print("- analysis_report.txt/json: 统计报告")


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
        return
    
    run_analysis_with_filter()


if __name__ == "__main__":
    main()
