#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
首投/复投功能演示脚本
Demo Script for Feed Type Analysis Functionality

功能：
1. 演示首投/复投分类逻辑
2. 展示实际数据分析结果
3. 演示筛选和统计功能
4. 提供功能概览

作者：AI Assistant
日期：2025-01-07
"""

import os
import pandas as pd
from crystal_data_analyzer import CrystalDataAnalyzer
from feed_type_classifier import FeedTypeClassifier


def demo_classification_logic():
    """演示分类逻辑"""
    print("🔬 首投/复投分类逻辑演示")
    print("=" * 50)
    print("\n判断规则：")
    print("• 如果文件中cc_feed_number字段有任何记录等于1 → 首投")
    print("• 如果文件中cc_feed_number字段没有任何记录等于1 → 复投")
    
    classifier = FeedTypeClassifier()
    
    print("\n📊 各种cc_feed_number值的分类结果：")
    print("-" * 40)
    
    test_values = [0, 1, 2, 7, 8, 10, 11, 21, 31, 1.0, "1", "11"]
    
    for value in test_values:
        result = classifier.classify_feed_type(value)
        emoji = "🥇" if result == "首投" else "🔄"
        print(f"{emoji} {value:>3} → {result}")


def demo_real_data_analysis():
    """演示真实数据分析"""
    print("\n\n🔍 真实数据分析演示")
    print("=" * 50)
    
    # 检查可用的数据文件
    demo_files = []
    for i in range(1, 6):
        folder = f"analoga{i:02d}"
        file_path = f"{folder}/extracted_content_1.csv"
        if os.path.exists(file_path):
            demo_files.append(file_path)
    
    if not demo_files:
        print("❌ 未找到演示数据文件")
        return
    
    print(f"📁 找到 {len(demo_files)} 个演示文件")
    
    # 创建分析器
    config = {
        "input_directory": ".",
        "output_directory": "demo_output",
        "time_format": "%Y-%m-%d %H:%M:%S",
        "preheating_duration_minutes": 10,
        "required_columns": [
            "cc_time", "cc_work_procedure", "cc_residue_weight",
            "cc_main_heating_display", "cc_vice_heating_set", "cc_ccd_liquid_temperature"
        ],
        "target_procedure": "预调温",
        "crystal_seeding_keyword": "引晶",
        "encoding": "utf-8",
        "feed_type_analysis": {
            "enabled": True,
            "feed_number_column": "cc_feed_number",
            "filter_type": "全部"
        }
    }
    
    import json
    with open("demo_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    
    try:
        analyzer = CrystalDataAnalyzer("demo_config.json")
        
        print("\n📋 分析结果：")
        print("-" * 80)
        print(f"{'文件夹':<12} {'类型':<6} {'cc_feed_number值':<20} {'等于1记录':<10} {'能效%':<8}")
        print("-" * 80)
        
        results = []
        for file_path in demo_files[:5]:  # 只演示前5个文件
            result = analyzer.analyze_single_csv(file_path)
            if result:
                folder = result['folder_name']
                feed_type = result['feed_type']
                feed_values = result['cc_feed_number_values']
                feed_1_records = result['feed_number_1_records']
                efficiency = result['energy_efficiency_percent']
                
                # 简化显示
                feed_values_short = feed_values.replace('[', '').replace(']', '').replace("'", "")
                
                emoji = "🥇" if feed_type == "首投" else "🔄"
                print(f"{folder:<12} {emoji}{feed_type:<5} {feed_values_short:<20} {feed_1_records:<10} {efficiency:<8.1f}")
                
                results.append(result)
        
        # 统计信息
        if results:
            first_count = len([r for r in results if r['feed_type'] == '首投'])
            repeat_count = len([r for r in results if r['feed_type'] == '复投'])
            
            print("\n📊 统计汇总：")
            print(f"  总文件数: {len(results)}")
            print(f"  🥇 首投文件: {first_count} ({first_count/len(results)*100:.1f}%)")
            print(f"  🔄 复投文件: {repeat_count} ({repeat_count/len(results)*100:.1f}%)")
            
            # 能效对比
            if first_count > 0 and repeat_count > 0:
                first_efficiencies = [r['energy_efficiency_percent'] for r in results if r['feed_type'] == '首投']
                repeat_efficiencies = [r['energy_efficiency_percent'] for r in results if r['feed_type'] == '复投']
                
                first_avg = sum(first_efficiencies) / len(first_efficiencies)
                repeat_avg = sum(repeat_efficiencies) / len(repeat_efficiencies)
                
                print(f"\n⚡ 能效对比：")
                print(f"  🥇 首投平均能效: {first_avg:.2f}%")
                print(f"  🔄 复投平均能效: {repeat_avg:.2f}%")
                
                if first_avg > repeat_avg:
                    print(f"  🏆 首投能效更高，高出 {first_avg - repeat_avg:.2f}%")
                elif repeat_avg > first_avg:
                    print(f"  🏆 复投能效更高，高出 {repeat_avg - first_avg:.2f}%")
                else:
                    print(f"  ⚖️ 首投和复投能效相当")
    
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
    finally:
        # 清理临时文件
        try:
            os.remove("demo_config.json")
        except:
            pass


def demo_filter_functionality():
    """演示筛选功能"""
    print("\n\n🔍 筛选功能演示")
    print("=" * 50)
    
    # 模拟数据
    sample_data = [
        {"folder": "analoga01", "feed_type": "复投", "cc_feed_number": "[7, 8]", "efficiency": 27.4},
        {"folder": "analoga02", "feed_type": "首投", "cc_feed_number": "[0, 1]", "efficiency": 22.9},
        {"folder": "analoga03", "feed_type": "首投", "cc_feed_number": "[1, 2]", "efficiency": 25.8},
        {"folder": "analoga04", "feed_type": "复投", "cc_feed_number": "[7, 8]", "efficiency": 24.1},
        {"folder": "analoga05", "feed_type": "首投", "cc_feed_number": "[1, 2]", "efficiency": 26.3},
    ]
    
    print("📋 原始数据：")
    print("-" * 60)
    for data in sample_data:
        emoji = "🥇" if data["feed_type"] == "首投" else "🔄"
        print(f"{emoji} {data['folder']} | {data['feed_type']} | {data['cc_feed_number']} | {data['efficiency']}%")
    
    # 筛选演示
    first_feed_data = [d for d in sample_data if d["feed_type"] == "首投"]
    repeat_feed_data = [d for d in sample_data if d["feed_type"] == "复投"]
    
    print(f"\n🥇 筛选结果 - 仅首投数据 ({len(first_feed_data)}个)：")
    print("-" * 60)
    for data in first_feed_data:
        print(f"🥇 {data['folder']} | {data['feed_type']} | {data['cc_feed_number']} | {data['efficiency']}%")
    
    print(f"\n🔄 筛选结果 - 仅复投数据 ({len(repeat_feed_data)}个)：")
    print("-" * 60)
    for data in repeat_feed_data:
        print(f"🔄 {data['folder']} | {data['feed_type']} | {data['cc_feed_number']} | {data['efficiency']}%")


def demo_usage_examples():
    """演示使用示例"""
    print("\n\n🚀 使用方法演示")
    print("=" * 50)
    
    print("1️⃣ 交互式分析器（推荐）：")
    print("   python interactive_feed_analyzer.py")
    print("   • 用户友好的图形界面")
    print("   • 支持实时选择筛选条件")
    print("   • 详细的统计报告和对比分析")
    
    print("\n2️⃣ 筛选脚本：")
    print("   python run_analysis_with_feed_filter.py")
    print("   • 命令行交互界面")
    print("   • 支持选择分析类型")
    
    print("\n3️⃣ 功能测试：")
    print("   python test_feed_functionality.py")
    print("   • 验证功能正确性")
    print("   • 测试分类逻辑")
    
    print("\n4️⃣ 配置文件方式：")
    print("   修改 config.json 中的 feed_type_analysis 配置")
    print("   然后运行: python crystal_data_analyzer.py")


def main():
    """主演示函数"""
    print("🎯 首投/复投功能完整演示")
    print("=" * 60)
    print("本演示将展示新增的首投/复投分析功能的各个方面")
    
    try:
        # 1. 分类逻辑演示
        demo_classification_logic()
        
        # 2. 真实数据分析演示
        demo_real_data_analysis()
        
        # 3. 筛选功能演示
        demo_filter_functionality()
        
        # 4. 使用方法演示
        demo_usage_examples()
        
        print("\n\n🎉 演示完成！")
        print("=" * 60)
        print("✅ 首投/复投功能已成功集成到项目中")
        print("✅ 所有功能测试通过")
        print("✅ 提供了完整的使用文档")
        print("\n📖 详细使用说明请参阅：首投复投功能使用指南.md")
        print("🚀 开始使用：python interactive_feed_analyzer.py")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        print("请检查数据文件和环境配置")


if __name__ == "__main__":
    main()
