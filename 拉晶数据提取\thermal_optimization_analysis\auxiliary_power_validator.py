"""
副加热功率验证器
基于能量守恒原理验证副加热功率的合理性
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class AuxiliaryPowerValidator:
    """副加热功率验证器"""
    
    def __init__(self):
        # 物理常数
        self.silicon_density = 2330  # kg/m³
        self.silicon_specific_heat = 0.7  # kJ/(kg·K)
        self.silicon_melting_point = 1414  # °C
        self.ambient_temp = 20  # °C
        
        # 设备参数
        self.thermal_efficiency = 0.28
        self.heat_loss_factor = 0.15  # 散热损失因子
        
    def calculate_theoretical_power_distribution(self, 
                                               weight: float, 
                                               duration: float,
                                               target_temp: float = 1450) -> Dict:
        """
        计算理论功率分配
        
        Args:
            weight: 硅重量 (kg)
            duration: 加热时长 (小时)
            target_temp: 目标温度 (°C)
            
        Returns:
            理论功率分配结果
        """
        # 计算理论热能需求
        sensible_heat = weight * self.silicon_specific_heat * (target_temp - self.ambient_temp)  # kJ
        theoretical_energy = sensible_heat / 3600  # kWh
        
        # 考虑散热损失的总能量需求
        heat_loss_energy = theoretical_energy * self.heat_loss_factor
        total_energy_needed = theoretical_energy + heat_loss_energy
        
        # 考虑热效率的实际输入能量
        actual_input_energy = total_energy_needed / self.thermal_efficiency
        
        # 平均功率需求
        avg_power_needed = actual_input_energy / duration  # kW
        
        # 理论功率分配策略
        # 主加热：承担主要加热任务，功率相对稳定
        # 副加热：用于精确温度控制和补偿散热损失
        
        # 基于经验和物理原理的功率分配
        main_power_ratio = 0.65  # 主加热承担65%
        aux_power_ratio = 0.35   # 副加热承担35%
        
        theoretical_main_power = avg_power_needed * main_power_ratio
        theoretical_aux_power = avg_power_needed * aux_power_ratio
        
        return {
            'theoretical_energy_kwh': theoretical_energy,
            'heat_loss_energy_kwh': heat_loss_energy,
            'total_energy_needed_kwh': total_energy_needed,
            'actual_input_energy_kwh': actual_input_energy,
            'avg_power_needed_kw': avg_power_needed,
            'theoretical_main_power_kw': theoretical_main_power,
            'theoretical_aux_power_kw': theoretical_aux_power,
            'main_power_ratio': main_power_ratio,
            'aux_power_ratio': aux_power_ratio
        }
    
    def validate_auxiliary_power(self, 
                                actual_main_power: float,
                                actual_aux_power: float,
                                weight: float,
                                duration: float) -> Dict:
        """
        验证副加热功率的合理性
        
        Args:
            actual_main_power: 实际主加热总能量 (kWh)
            actual_aux_power: 实际副加热总能量 (kWh)
            weight: 重量 (kg)
            duration: 持续时间 (小时)
            
        Returns:
            验证结果
        """
        # 计算理论功率分配
        theoretical = self.calculate_theoretical_power_distribution(weight, duration)
        
        # 实际功率分析
        total_actual_energy = actual_main_power + actual_aux_power
        actual_main_ratio = actual_main_power / total_actual_energy if total_actual_energy > 0 else 0
        actual_aux_ratio = actual_aux_power / total_actual_energy if total_actual_energy > 0 else 0
        
        # 功率比例偏差分析
        main_ratio_deviation = abs(actual_main_ratio - theoretical['main_power_ratio'])
        aux_ratio_deviation = abs(actual_aux_ratio - theoretical['aux_power_ratio'])
        
        # 能量效率分析
        energy_efficiency = theoretical['theoretical_energy_kwh'] / total_actual_energy if total_actual_energy > 0 else 0
        
        # 副加热功率合理性判断
        aux_power_reasonable = (
            0.2 <= actual_aux_ratio <= 0.6 and  # 副加热比例在合理范围
            aux_ratio_deviation < 0.2 and       # 偏差不超过20%
            0.15 <= energy_efficiency <= 0.4    # 整体效率合理
        )
        
        # 功率配比优化建议
        if actual_aux_ratio < 0.2:
            optimization_suggestion = "副加热功率过低，建议增加副加热功率以提高温度控制精度"
        elif actual_aux_ratio > 0.6:
            optimization_suggestion = "副加热功率过高，建议降低副加热功率，增加主加热功率"
        elif aux_ratio_deviation > 0.2:
            optimization_suggestion = f"功率配比偏差较大，建议调整至主加热{theoretical['main_power_ratio']:.1%}，副加热{theoretical['aux_power_ratio']:.1%}"
        else:
            optimization_suggestion = "功率配比合理，建议保持当前策略"
        
        return {
            'theoretical_analysis': theoretical,
            'actual_main_ratio': actual_main_ratio,
            'actual_aux_ratio': actual_aux_ratio,
            'main_ratio_deviation': main_ratio_deviation,
            'aux_ratio_deviation': aux_ratio_deviation,
            'energy_efficiency': energy_efficiency,
            'aux_power_reasonable': aux_power_reasonable,
            'total_energy_deviation': abs(total_actual_energy - theoretical['actual_input_energy_kwh']) / theoretical['actual_input_energy_kwh'],
            'optimization_suggestion': optimization_suggestion
        }
    
    def batch_validate_auxiliary_power(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        批量验证副加热功率
        
        Args:
            df: 包含功率和重量数据的DataFrame
            
        Returns:
            验证结果DataFrame
        """
        validation_results = []
        
        for idx, row in df.iterrows():
            main_power = row.get('main_total_energy_kwh', 0)
            aux_power = row.get('vice_total_energy_kwh', 0)
            weight = row.get('weight_difference', 0)
            duration = row.get('duration_hours', 0)
            
            if weight > 0 and duration > 0 and (main_power + aux_power) > 0:
                validation = self.validate_auxiliary_power(
                    actual_main_power=main_power,
                    actual_aux_power=aux_power,
                    weight=weight,
                    duration=duration
                )
                
                result = {
                    'file_index': idx,
                    'weight_kg': weight,
                    'duration_hours': duration,
                    'actual_main_power_kwh': main_power,
                    'actual_aux_power_kwh': aux_power,
                    'total_actual_energy_kwh': main_power + aux_power,
                    'theoretical_energy_kwh': validation['theoretical_analysis']['theoretical_energy_kwh'],
                    'theoretical_total_energy_kwh': validation['theoretical_analysis']['actual_input_energy_kwh'],
                    'actual_main_ratio': validation['actual_main_ratio'],
                    'actual_aux_ratio': validation['actual_aux_ratio'],
                    'theoretical_main_ratio': validation['theoretical_analysis']['main_power_ratio'],
                    'theoretical_aux_ratio': validation['theoretical_analysis']['aux_power_ratio'],
                    'main_ratio_deviation': validation['main_ratio_deviation'],
                    'aux_ratio_deviation': validation['aux_ratio_deviation'],
                    'energy_efficiency': validation['energy_efficiency'],
                    'aux_power_reasonable': validation['aux_power_reasonable'],
                    'total_energy_deviation': validation['total_energy_deviation'],
                    'optimization_suggestion': validation['optimization_suggestion']
                }
                
                validation_results.append(result)
        
        return pd.DataFrame(validation_results)
    
    def create_validation_visualizations(self, validation_df: pd.DataFrame):
        """创建验证结果可视化"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('副加热功率验证分析', fontsize=16, fontweight='bold')
        
        # 1. 实际vs理论功率比例对比
        x = np.arange(len(validation_df))
        width = 0.35
        
        axes[0, 0].bar(x - width/2, validation_df['actual_main_ratio'], width, 
                      label='实际主加热比例', alpha=0.8)
        axes[0, 0].bar(x + width/2, validation_df['theoretical_main_ratio'], width, 
                      label='理论主加热比例', alpha=0.8)
        axes[0, 0].set_xlabel('样本序号')
        axes[0, 0].set_ylabel('功率比例')
        axes[0, 0].set_title('主加热功率比例对比')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 副加热功率比例分布
        axes[0, 1].hist(validation_df['actual_aux_ratio'], bins=20, alpha=0.7, 
                       color='orange', edgecolor='black', label='实际分布')
        axes[0, 1].axvline(validation_df['theoretical_aux_ratio'].iloc[0], 
                          color='red', linestyle='--', label='理论值')
        axes[0, 1].axvspan(0.2, 0.6, alpha=0.2, color='green', label='合理范围')
        axes[0, 1].set_xlabel('副加热功率比例')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].set_title('副加热功率比例分布')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 能量效率分析
        axes[0, 2].scatter(validation_df['total_actual_energy_kwh'], 
                          validation_df['energy_efficiency'],
                          c=validation_df['aux_power_reasonable'].astype(int),
                          cmap='RdYlGn', alpha=0.7)
        axes[0, 2].set_xlabel('总输入能量 (kWh)')
        axes[0, 2].set_ylabel('能量效率')
        axes[0, 2].set_title('能量效率分析')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 功率比例偏差分析
        axes[1, 0].scatter(validation_df['main_ratio_deviation'], 
                          validation_df['aux_ratio_deviation'],
                          c=validation_df['energy_efficiency'], 
                          cmap='viridis', alpha=0.7)
        axes[1, 0].set_xlabel('主加热比例偏差')
        axes[1, 0].set_ylabel('副加热比例偏差')
        axes[1, 0].set_title('功率比例偏差分析')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 重量vs功率关系
        axes[1, 1].scatter(validation_df['weight_kg'], 
                          validation_df['actual_aux_ratio'],
                          c=validation_df['duration_hours'], 
                          cmap='plasma', alpha=0.7)
        axes[1, 1].set_xlabel('重量 (kg)')
        axes[1, 1].set_ylabel('副加热功率比例')
        axes[1, 1].set_title('重量-副加热功率关系')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 合理性统计
        reasonable_count = validation_df['aux_power_reasonable'].sum()
        total_count = len(validation_df)
        unreasonable_count = total_count - reasonable_count
        
        axes[1, 2].pie([reasonable_count, unreasonable_count], 
                      labels=['合理', '不合理'],
                      colors=['lightgreen', 'lightcoral'],
                      autopct='%1.1f%%',
                      startangle=90)
        axes[1, 2].set_title('副加热功率合理性统计')
        
        plt.tight_layout()
        plt.savefig('auxiliary_power_validation.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_validation_report(self, validation_df: pd.DataFrame) -> str:
        """生成验证报告"""
        total_samples = len(validation_df)
        reasonable_rate = validation_df['aux_power_reasonable'].mean()
        avg_aux_ratio = validation_df['actual_aux_ratio'].mean()
        avg_efficiency = validation_df['energy_efficiency'].mean()
        avg_aux_deviation = validation_df['aux_ratio_deviation'].mean()
        
        report = f"""
副加热功率验证报告
==================================================

验证统计:
------------------------------
总样本数: {total_samples}
副加热功率合理率: {reasonable_rate:.1%}
平均副加热功率比例: {avg_aux_ratio:.3f}
平均能量效率: {avg_efficiency:.3f}
平均副加热比例偏差: {avg_aux_deviation:.3f}

详细分析:
------------------------------
理论最优副加热比例: 35%
实际副加热比例范围: {validation_df['actual_aux_ratio'].min():.3f} - {validation_df['actual_aux_ratio'].max():.3f}
能量效率范围: {validation_df['energy_efficiency'].min():.3f} - {validation_df['energy_efficiency'].max():.3f}

优化建议:
------------------------------
1. 功率配比优化: 建议将副加热功率比例控制在20%-60%范围内
2. 当前{reasonable_rate:.1%}的样本功率配比合理，需要优化{(1-reasonable_rate):.1%}的样本
3. 建议根据重量和持续时间动态调整功率配比
4. 对于能量效率低于15%的样本，需要检查设备状态和加热策略

技术实现建议:
------------------------------
1. 建立基于重量和时间的动态功率分配模型
2. 实时监控能量效率，及时调整功率配比
3. 设置功率比例偏差报警机制
4. 定期校准理论模型参数
"""
        
        return report
