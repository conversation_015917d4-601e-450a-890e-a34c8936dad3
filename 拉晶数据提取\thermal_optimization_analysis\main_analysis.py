"""
热量优化分析主程序
基于能量守恒的拉晶设备热量管理优化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from datetime import datetime
from typing import Dict
import warnings
warnings.filterwarnings('ignore')

from thermal_risk_analyzer import ThermalRiskAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ThermalOptimizationAnalysis:
    """热量优化分析主类"""
    
    def __init__(self, data_path: str = "../output_results"):
        self.data_path = Path(data_path)
        self.analyzer = ThermalRiskAnalyzer()
        self.results = {}
        
    def load_processed_data(self) -> pd.DataFrame:
        """加载已处理的数据"""
        try:
            # 尝试加载汇总数据
            summary_file = self.data_path / "all_folders_summary.csv"
            if summary_file.exists():
                df = pd.read_csv(summary_file)
                print(f"成功加载汇总数据: {len(df)} 条记录")
                return df
            else:
                print("未找到汇总数据文件，请先运行数据提取程序")
                return pd.DataFrame()
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return pd.DataFrame()
    
    def analyze_energy_conservation(self, df: pd.DataFrame) -> Dict:
        """分析能量守恒情况"""
        print("开始能量守恒分析...")
        
        conservation_results = []
        
        for idx, row in df.iterrows():
            # 提取必要数据
            main_power_total = row.get('main_total_energy_kwh', 0)
            aux_power_total = row.get('vice_total_energy_kwh', 0)
            theoretical_energy = row.get('silicon_thermal_energy_kwh', 0)
            weight = row.get('weight_difference', 0)
            duration = row.get('duration_hours', 0)
            
            if weight > 0 and duration > 0:
                # 进行能量守恒验证
                verification = self.analyzer.energy_conservation_verification(
                    main_power_total=main_power_total,
                    aux_power_total=aux_power_total,
                    theoretical_energy=theoretical_energy,
                    weight=weight,
                    duration=duration
                )
                
                verification['file_index'] = idx
                verification['weight'] = weight
                verification['duration'] = duration
                conservation_results.append(verification)
        
        conservation_df = pd.DataFrame(conservation_results)
        
        # 统计分析
        stats = {
            'total_samples': len(conservation_df),
            'avg_energy_efficiency': conservation_df['energy_efficiency'].mean(),
            'avg_aux_power_ratio': conservation_df['aux_power_ratio'].mean(),
            'energy_conservation_valid_rate': conservation_df['energy_conservation_valid'].mean(),
            'aux_power_reasonable_rate': conservation_df['aux_power_reasonable'].mean(),
            'overall_efficiency_reasonable_rate': conservation_df['overall_efficiency_reasonable'].mean()
        }
        
        return {
            'conservation_data': conservation_df,
            'statistics': stats
        }
    
    def analyze_thermal_risk_patterns(self, df: pd.DataFrame) -> Dict:
        """分析热量风险模式"""
        print("开始热量风险模式分析...")
        
        # 模拟时间序列数据分析（基于现有数据）
        risk_patterns = []
        
        for idx, row in df.iterrows():
            # 模拟不同阶段的风险分析
            weight = row.get('weight_difference', 450)
            duration = row.get('duration_hours', 8)
            main_power = row.get('first_crystal_seeding_main_power_kw', 60)
            aux_power = row.get('vice_total_energy_kwh', 0) / duration if duration > 0 else 30
            
            # 模拟不同时间点的风险
            time_points = np.linspace(0, duration, min(int(duration * 10), 100))
            
            for t in time_points:
                # 模拟温度变化（简化模型）
                temp_progress = t / duration
                current_temp = 20 + (1450 - 20) * temp_progress
                
                risk = self.analyzer.calculate_thermal_accumulation_risk(
                    current_temp=current_temp,
                    target_temp=1450,
                    main_power=main_power,
                    aux_power=aux_power,
                    weight=weight,
                    time_interval=0.1
                )
                
                dynamic_threshold = self.analyzer.calculate_solution_ratio_threshold(risk)
                
                risk_patterns.append({
                    'file_index': idx,
                    'time_hours': t,
                    'temperature': current_temp,
                    'thermal_risk': risk,
                    'dynamic_threshold': dynamic_threshold,
                    'weight': weight,
                    'main_power': main_power,
                    'aux_power': aux_power
                })
        
        risk_df = pd.DataFrame(risk_patterns)
        
        # 风险统计
        risk_stats = {
            'avg_risk': risk_df['thermal_risk'].mean(),
            'max_risk': risk_df['thermal_risk'].max(),
            'high_risk_rate': (risk_df['thermal_risk'] > 0.7).mean(),
            'avg_dynamic_threshold': risk_df['dynamic_threshold'].mean(),
            'threshold_adjustment_range': (
                risk_df['dynamic_threshold'].min(),
                risk_df['dynamic_threshold'].max()
            )
        }
        
        return {
            'risk_data': risk_df,
            'risk_statistics': risk_stats
        }
    
    def create_optimization_visualizations(self, conservation_data: Dict, risk_data: Dict):
        """创建优化分析可视化图表"""
        print("生成优化分析图表...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('热量优化分析结果', fontsize=16, fontweight='bold')
        
        # 1. 能量守恒验证
        conservation_df = conservation_data['conservation_data']
        axes[0, 0].scatter(conservation_df['theoretical_energy_kwh'], 
                          conservation_df['total_input_energy_kwh'],
                          alpha=0.6, c=conservation_df['energy_efficiency'], 
                          cmap='viridis')
        axes[0, 0].plot([0, conservation_df['theoretical_energy_kwh'].max()], 
                       [0, conservation_df['theoretical_energy_kwh'].max()], 
                       'r--', label='理想线')
        axes[0, 0].set_xlabel('理论能量需求 (kWh)')
        axes[0, 0].set_ylabel('实际输入能量 (kWh)')
        axes[0, 0].set_title('能量守恒验证')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 副加热功率比例分析
        axes[0, 1].hist(conservation_df['aux_power_ratio'], bins=30, alpha=0.7, 
                       color='orange', edgecolor='black')
        axes[0, 1].axvline(conservation_df['aux_power_ratio'].mean(), 
                          color='red', linestyle='--', 
                          label=f'平均值: {conservation_df["aux_power_ratio"].mean():.3f}')
        axes[0, 1].axvspan(0.2, 0.6, alpha=0.2, color='green', label='合理范围')
        axes[0, 1].set_xlabel('副加热功率比例')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].set_title('副加热功率比例分布')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 能量效率分析
        axes[0, 2].boxplot([conservation_df['energy_efficiency']], 
                          labels=['能量效率'])
        axes[0, 2].axhspan(0.15, 0.35, alpha=0.2, color='green', label='合理范围')
        axes[0, 2].set_ylabel('效率')
        axes[0, 2].set_title('能量效率分布')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 热量风险时间序列
        risk_df = risk_data['risk_data']
        sample_files = risk_df['file_index'].unique()[:5]  # 选择前5个文件作为示例
        
        for file_idx in sample_files:
            file_data = risk_df[risk_df['file_index'] == file_idx]
            axes[1, 0].plot(file_data['time_hours'], file_data['thermal_risk'], 
                           alpha=0.7, label=f'文件 {file_idx}')
        
        axes[1, 0].axhline(0.7, color='red', linestyle='--', label='高风险阈值')
        axes[1, 0].set_xlabel('时间 (小时)')
        axes[1, 0].set_ylabel('热量积累风险')
        axes[1, 0].set_title('热量风险时间序列')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 动态阈值调整
        axes[1, 1].scatter(risk_df['thermal_risk'], risk_df['dynamic_threshold'], 
                          alpha=0.6, c=risk_df['temperature'], cmap='coolwarm')
        axes[1, 1].set_xlabel('热量积累风险')
        axes[1, 1].set_ylabel('动态阈值')
        axes[1, 1].set_title('动态阈值调整策略')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 温度-功率关系
        axes[1, 2].scatter(risk_df['temperature'], risk_df['main_power'], 
                          alpha=0.6, c=risk_df['thermal_risk'], cmap='Reds')
        axes[1, 2].set_xlabel('温度 (°C)')
        axes[1, 2].set_ylabel('主功率 (kW)')
        axes[1, 2].set_title('温度-功率关系')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('thermal_optimization_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_optimization_report(self, conservation_data: Dict, risk_data: Dict):
        """生成优化分析报告"""
        print("生成优化分析报告...")
        
        conservation_stats = conservation_data['statistics']
        risk_stats = risk_data['risk_statistics']
        
        report = f"""
热量优化分析报告
==================================================
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

能量守恒验证结果:
------------------------------
总样本数: {conservation_stats['total_samples']}
平均能量效率: {conservation_stats['avg_energy_efficiency']:.3f}
平均副加热功率比例: {conservation_stats['avg_aux_power_ratio']:.3f}
能量守恒有效率: {conservation_stats['energy_conservation_valid_rate']:.3f}
副加热功率合理率: {conservation_stats['aux_power_reasonable_rate']:.3f}
整体效率合理率: {conservation_stats['overall_efficiency_reasonable_rate']:.3f}

热量风险分析结果:
------------------------------
平均热量风险: {risk_stats['avg_risk']:.3f}
最大热量风险: {risk_stats['max_risk']:.3f}
高风险比例: {risk_stats['high_risk_rate']:.3f}
平均动态阈值: {risk_stats['avg_dynamic_threshold']:.3f}
阈值调整范围: {risk_stats['threshold_adjustment_range'][0]:.3f} - {risk_stats['threshold_adjustment_range'][1]:.3f}

优化建议:
------------------------------
1. 副加热功率控制: 建议将副加热功率比例控制在20%-60%范围内
2. 动态阈值调整: 根据热量积累风险动态调整溶液比阈值，风险高时提前关闭底加热
3. 能量效率优化: 当前平均效率为{conservation_stats['avg_energy_efficiency']:.1%}，建议通过优化加热策略提升至25%-35%
4. 风险预警: 当热量积累风险超过0.7时，应立即调整加热功率或提前关闭底加热

技术实现要点:
------------------------------
1. 实时监控热量积累风险指标
2. 基于风险评估动态调整控制参数
3. 建立能量平衡验证机制
4. 优化主副加热功率配比策略
"""
        
        # 保存报告
        with open('thermal_optimization_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("报告已保存到 thermal_optimization_report.txt")
        return report
    
    def run_complete_analysis(self):
        """运行完整的优化分析"""
        print("开始热量优化分析...")
        
        # 加载数据
        df = self.load_processed_data()
        if df.empty:
            print("无法加载数据，分析终止")
            return
        
        # 能量守恒分析
        conservation_results = self.analyze_energy_conservation(df)
        
        # 热量风险分析
        risk_results = self.analyze_thermal_risk_patterns(df)
        
        # 生成可视化
        self.create_optimization_visualizations(conservation_results, risk_results)
        
        # 生成报告
        report = self.generate_optimization_report(conservation_results, risk_results)
        
        # 保存结果
        self.results = {
            'conservation_analysis': conservation_results,
            'risk_analysis': risk_results,
            'report': report
        }
        
        print("热量优化分析完成！")
        return self.results

if __name__ == "__main__":
    # 运行分析
    analyzer = ThermalOptimizationAnalysis()
    results = analyzer.run_complete_analysis()
