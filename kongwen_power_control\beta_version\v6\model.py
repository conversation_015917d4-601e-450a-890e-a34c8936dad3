import pandas as pd
import yaml
import pickle
import numpy as np
from os.path import join
import math
from datetime import datetime
import os
import time

# 副功率预测模型导入 - 融合v3版本的导入逻辑
try:
    import sys
    import importlib.util
    from pathlib import Path

    # 方法1: 尝试相对导入 (IDE 友好)
    try:
        from .production_deployment.src.predict import VicePowerPredictor
        VICE_POWER_PREDICTOR_AVAILABLE = True
        print("✅ 使用 production_deployment 预测器 (相对导入)")
    except ImportError:
        # 方法2: 使用 importlib 动态导入 (IDE 友好的动态导入)
        try:
            predict_module_path = Path(__file__).parent / 'production_deployment' / 'src' / 'predict.py'
            if predict_module_path.exists():
                spec = importlib.util.spec_from_file_location("predict", predict_module_path)
                predict_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(predict_module)
                VicePowerPredictor = predict_module.VicePowerPredictor
                VICE_POWER_PREDICTOR_AVAILABLE = True
                print("✅ 使用 production_deployment 预测器 (importlib动态导入)")
            else:
                raise ImportError(f"预测器模块文件不存在: {predict_module_path}")
        except Exception as e:
            # 方法3: 传统sys.path方式作为最后备选
            production_src_path = str(Path(__file__).parent / 'production_deployment' / 'src')
            if production_src_path not in sys.path:
                sys.path.append(production_src_path)
            # 使用 importlib 而不是直接 import 来避免IDE警告
            import importlib
            predict_module = importlib.import_module('predict')
            VicePowerPredictor = predict_module.VicePowerPredictor
            VICE_POWER_PREDICTOR_AVAILABLE = True
            print("✅ 使用 production_deployment 预测器 (sys.path + importlib)")

except ImportError as e:
    print(f"Warning: 副功率预测系统不可用: {e}")
    VICE_POWER_PREDICTOR_AVAILABLE = False

# from kongwen_power_control.beta_version.v1.dynamicheating import calculate

class KongwenGonglvCorrectionModel():
    # Phase
    INIT = 0
    VICE_CLOSE1 = 1
    VICE_CLOSE2 = 2
    VICE_REOPEN = 3
    ALMOST_DONE = 4
    DONE = 5

    @classmethod
    def from_path(cls, config_path):
        m = cls()
        m.load_config(config_path)
        return m

    def find_close_time_range(self, history_data, current_data):

        try:

            current_bucket = current_data['feed_data'][1]
            current_feed_duration = current_data['feed_data'][2]
            current_last_data = current_data['last_data'][0]
            current_crystal_power = current_data.get('crystal_power', 60)  # 默认值处理
            filtered = []
            filtered2 = []

            # 筛选加料桶数相同且target_tem在1444到1448之间的记录
            for data in history_data:
                # 执行联合筛选
                conditions = [
                    data['feed_data'][1] == current_bucket,  # 桶数相同
                    1444 <= data['target_tem'] <= 1452,  # 温度范围
                    abs(data['feed_data'][2] - current_feed_duration) <= 50,  # 加料时间差
                    abs(data.get('crystal_power', 0) - current_crystal_power) <= 10  # 功率差
                ]
                if all(conditions):
                    filtered.append(data)

            # 处理筛选结果为空的情况
            if not filtered:
                return [None, None]

            # 处理筛选结果为一条的情况
            if len(filtered) == 1:
                single = filtered[0]
                if single['target_tem'] < 1448:
                    # 温度小于1448，作为下限
                    return [single['close_time'], None]
                else:
                    # 温度等于1448，作为上限
                    return [None, single['close_time']]

            # 处理多条数据情况，取最大最小值
            # halve_times = [d['halve_time'] for d in filtered]
            close_times = [d['close_time'] for d in filtered]
            Min, Max = min(close_times), max(close_times)

            '''找最后一桶加料信息更新关底加时间'''
            for data in history_data:
                # 执行联合筛选
                conditions2 = [
                    data['feed_data'][1] == current_bucket,  # 桶数相同
                    abs(data['feed_data'][2] - current_feed_duration) <= 50,  # 加料时间差
                    abs(data.get('crystal_power', 0) - current_crystal_power) <= 10,  # 功率差
                    abs(data['last_data'][0] - current_last_data) <= 5  # 新增条件：最后一桶加料量差<=5kg
                ]
                if all(conditions2):
                    filtered2.append(data)
            if not filtered2:
                current_interval = current_last_data[2]
                closest_record = min(filtered2, key=lambda x: abs(x['last_data'][2] - current_interval))
                if abs(closest_record['last_data'][2] - current_interval) <= 5:
                    if closest_record['target_tem'] >= 1450 and Min < closest_record['close_time'] < Max:
                        Max = closest_record['close_time']
                    if closest_record['target_tem'] <= 1446 and Min < closest_record['close_time'] < Max:
                        Min = closest_record['close_time']

            return [Min, Max]
        except Exception as e:
            return [None, None]

    def load_config(self, config_path):
        with open(config_path, encoding='utf-8') as f:
            config = yaml.load(f, Loader=yaml.FullLoader)['gonglv']
        self.config = config
        self.origin_vice_close_ratio = config['vice_close_ratio']  # [half, all]
        self.high_ratio = config['high_ratio']
        self.time_range = config['time_range']
        self.origin_done_power = config['done_power']
        self.done_power_k = config['done_power_k']
        self.origin_init_power = config['init_power']
        self.begin_t = config['begin_t']
        self.undo_main_ratio = config['undo_main_ratio']
        self.done_ratio = config['done_ratio']
        self.down_time = config['down_time']
        self.vice_check_time = config['vice_check_time']
        self.max_reopen_time = config['max_reopen_time']
        self.key_ccd = [0] + config['key_ccd'] + [10000]
        self.origin_key_power = config['key_power']
        self.key_power_k = config['key_power_k']
        self.vice_time_range = config['vice_time_range']
        self.melting_ccd3 = config['melting_ccd3']
        self.melting_power_k = config['melting_power_k']
        self.dynamic_vice_heating = config['dynamic_vice_heating']
        self.kongwen_target = 1448
        self.ladder_vice_power = 0
        self.turnover_threshold, self.film_threshold = 40, 40  # 翻料占比阈值，薄膜料占比阈值
        self.adjust_space = 10  # 薄膜料调整空间
        self.turnover_delay = [5, 5]  # 翻料延时
        self.one_vice_close_ratio = [25, 40]  # 加一桶料的溶液比阈值
        self.main_delay = 5  # 主加调整延时

    def load_model(self, keycurves_path):
        with open(keycurves_path, 'rb') as f:
            self.keycurves = pickle.load(f)
        return len(self.keycurves) != 0

    def generate_thresholds_and_powers(self, start, end):
        # 确保从 start 到 end 总共选取 4 个溶液比值
        power_ratios = [0.8, 0.6, 0.3, 0.0]  # 固定的功率比例
        thresholds = [start + i * (end - start) / (len(power_ratios) - 1) for i in range(len(power_ratios))]  # 等间隔选取4个值

        # 生成溶液比和功率比例的配对，使用列表而不是元组
        threshold_power_pairs = [[thresholds[i], power_ratios[i]] for i in range(len(power_ratios))]

        return threshold_power_pairs

    def setup(self, device_id, jialiao, times, power_yinjing, init_power, config, field_size, product_type, target_ccd,
              history_data, feeding_type=1):

        update_keys = ['high_ratio', 'time_range', 'done_power_k', 'begin_t', 'undo_main_ratio', 'done_ratio',
                       'down_time', 'vice_check_time', 'max_reopen_time', 'key_power_k', 'vice_time_range',
                       'melting_ccd3', 'melting_power_k', 'dynamic_vice_heating', 'kongwen_target', 'ladder_vice_power',
                       'turnover_threshold', 'film_threshold', 'one_vice_close_ratio', 'adjust_space', 'turnover_delay',
                       'main_delay']
        for key in update_keys:
            if key in config and config[key] is not None:
                setattr(self, key, config[key])

        if float(product_type) in [11, 12] and float(field_size) == 36:
            if 'time_range' not in config:
                self.time_range = [self.config['time_range'][0] - 2, self.config['time_range'][1] + 5]
                print(self.time_range)
            if 'done_power_k' not in config:
                self.done_power_k = [
                    self.config['done_power_k'][0] + 0.25,
                    self.config['done_power_k'][1] + 0.02,
                    self.config['done_power_k'][2],
                    self.config['done_power_k'][3]
                ]
        self.vice_close_ratio = None
        if 'vice_ratios' in config:
            vice_ratios = config['vice_ratios']
            if jialiao < vice_ratios[0][0]:
                self.vice_close_ratio = (vice_ratios[0][2], vice_ratios[0][3])
            elif jialiao > vice_ratios[-1][1]:
                self.vice_close_ratio = (vice_ratios[-1][2], vice_ratios[-1][3])
            else:
                for w1, w2, r1, r2 in vice_ratios:
                    if w1 <= jialiao <= w2:
                        self.vice_close_ratio = (r1, r2)
                        break
        if self.vice_close_ratio is None:
            if jialiao < 200:
                self.vice_close_ratio = [self.origin_vice_close_ratio[0] - 20] + [self.origin_vice_close_ratio[1] - 10]
            else:
                self.vice_close_ratio = self.origin_vice_close_ratio
        self.init_main_power = init_power[0] if init_power[0] is not None else self.origin_init_power[0]
        self.init_vice_power = init_power[1] if init_power[1] is not None else self.origin_init_power[1]
        if power_yinjing is None:
            self.mid_power, self.min_main_power, self.end_power, self.down_clip = self.origin_done_power
            self.key_power = self.origin_key_power
            self.vice_reopen_power = self.init_vice_power * 0.8
        else:
            k_func = lambda k: np.clip(power_yinjing * k, 30, 100)
            self.mid_power, self.min_main_power, self.end_power, self.down_clip = list(map(k_func, self.done_power_k))
            self.key_power = list(map(k_func, self.key_power_k))
            self.vice_reopen_power = power_yinjing * 0.5
        if power_yinjing == 0: power_yinjing = 65
        self.phase = __class__.INIT
        self.vice_half_closed_time, self.vice_closed_time, self.vice_reopen_time, self.vice_reopen_ratio, self.high_time, self.done_time = None, None, None, None, None, None
        self.cur_main_power, self.cur_vice_power = self.init_main_power, self.init_vice_power
        self.reopened, self.location_fixed = False, False
        self.before_ratios = []
        self.reopened_ratios = []
        self.before_ratios_done = []
        self.before_reopened_ratios = []
        self.Quanrong = None
        self.power_change = None
        self.power_yinjing = power_yinjing
        self.product_type, self.field_size, self.jialiao = product_type, field_size, jialiao
        self.ccd3_power_adjust, self.dynamic_main_heating, self.device_id, self.jialiao = False, 0, device_id, jialiao
        self.film_ratio, self.film_correction = [70, 80], None
        self.fime_data = []
        self.dynamic_setting = False
        self.dynamic_result = None
        self.target_ccd = target_ccd
        self.risk, self.history_close_vice_time, self.history_vice_ratio, self.history_fullmelting_time = None, None, None, None
        self.history_time_range, self.limit_range = None, None
        self.over_temp_ratio, self.under_temp_ratio = None, None
        self.vice_lower, self.vice_upper = None, None
        self.power_zeroed = False
        self.realtime_data = []
        self.turnover_data = []
        self.turnover_data_filter = []
        self.fime_data_filter = []
        self.ratio_data = []
        self.ratio_thresholds = self.generate_thresholds_and_powers(self.vice_close_ratio[0] - 5,
                                                                    self.vice_close_ratio[1] + 5)  # 这里需要配置溶液比阈值

        '''finish相关参数'''
        self.start_time = datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        self.total_duration, self.feed_data, self.last_data, self.last_but_one_data, self.reopen = None, [], [], [], 0
        self.target_tem, self.first_valid_ccd, self.first_valid_time = None, None, None

        self.history_data = history_data

        if device_id == 'A37' or device_id == 'A39':
            self.mid_power = init_power[0]
        if float(field_size) == 36:
            self.ekf = RealTimeEKF(process_variance=9e-4, measurement_variance=0.05, max_increase_rate=0.3,
                                   rate_threshold=3, upper_limit=100)
        else:
            self.ekf = RealTimeEKF(process_variance=1e-3, measurement_variance=0.05, max_increase_rate=0.2,
                                   rate_threshold=3, upper_limit=100)

        # 保存投料类型参数
        self.feeding_type = feeding_type  # 0: 首投, 1: 复投

        # 初始化副功率预测器
        self.vice_power_predictor = None

        # 初始化实时副功率相关参数
        self.real_time_vice_power = 80  # 默认初始值
        self.vice_power_cumulative = 0.0  # 副功率累积输出值 (kWh)
        self.vice_power_shutdown = False  # 副功率是否已关闭

        # time_interval 和 cumulative_feed_weight 参数现在都从 predict 方法实时传入，不在 setup 中处理

        if VICE_POWER_PREDICTOR_AVAILABLE:
            try:
                # 使用正确的 production_deployment 模型路径
                models_dir = str(Path(__file__).parent / 'production_deployment' / 'models')
                self.vice_power_predictor = VicePowerPredictor(models_dir=models_dir)
                print(f"✅ Production 副功率预测器初始化成功 - 设备: {device_id}")

            except Exception as e:
                print(f"❌ 副功率预测器初始化失败: {e}")
                self.vice_power_predictor = None

    def finish(self, end_code):
        result = {
            'product_type': self.product_type,
            'field_size': self.field_size,
            'device_id': self.device_id,
            'start_time': self.start_time,
            'total_duration': self.total_duration,
            'yinjing_power': self.power_yinjing,
            'feed_data': self.feed_data,
            'last_data': self.last_data,
            'last_but_one_data': self.last_but_one_data,
            'halve_time': self.vice_half_closed_time,
            'close_time': self.vice_closed_time,
            'reopen': self.reopen,
            'fullmelt_time': self.total_duration - self.vice_closed_time,
            'target_tem': self.target_tem,
        }
        return result

    def calculate_recent_extremes_mean(self, data):
        try:
            if len(data) < 2:
                return None  # 不足两个数据点无法计算极值
            recent_max = None
            recent_min = None
            # 从最后一个元素向前查找
            for value in reversed(data):
                if recent_max is None or value > recent_max:
                    recent_max = value
                if recent_min is None or value < recent_min:
                    recent_min = value
                # 如果已经找到了极大值和极小值，可以停止
                if recent_max is not None and recent_min is not None:
                    break
            # 计算并返回均值
            if recent_max is not None and recent_min is not None:
                mean_value = (recent_max + recent_min) / 2
                return mean_value
            return None
        except Exception as e:
            return None

    def is_empty(self, time_range):
        return any(x is None or (isinstance(x, float) and math.isnan(x)) for x in time_range)

    def set_vice_closed_values(self, t):
        """封装 self.phase, self.cur_vice_power, self.vice_closed_time 的赋值"""
        self.phase, self.cur_vice_power, self.vice_closed_time = __class__.VICE_CLOSE2, 0, t

    def control_vice_power(self, data, ratio_thresholds, initial_power, t, vice_lower, vice_upper):
        """
        控制副功率，根据滑动窗口内的溶液比来调整副功率。

        参数：
        - data: 存储历史溶液比和时间的数组（deque 类型）
        - ratio_thresholds: 溶液比对应副功率比例的二维数组，例如 [[20, 0.8], [25, 0.5], [30, 0.3], [35, 0.0]]
        - initial_power: 初始功率
        - t: 当前时间（秒）
        - ratio: 当前溶液比
        返回：
        - 返回调整后的副功率
        """
        # 记录当前的溶液比和时间
        if self.power_zeroed:
            return 0

        # 获取最近5分钟的数据（即从 t 到 t - 300s 之间的数据）
        recent_ratios = [r for time, r in data if t - time <= 300]

        # 如果最近5分钟的溶液比数据不足，则返回 None
        if len(recent_ratios) == 0:
            return initial_power  # 没有足够数据来计算副功率

        # 获取滑动窗口内的最大溶液比
        max_ratio = min(recent_ratios)

        # 如果最大溶液比小于20，则副功率为初始功率
        if max_ratio < ratio_thresholds[0][0]:
            print(f"溶液比小于20，副功率保持为初始功率 {initial_power}")
            self.last_power = initial_power
            return initial_power

        # 根据溶液比的范围调整副功率
        for i in range(1, len(ratio_thresholds)):
            lower_threshold = ratio_thresholds[i - 1][0]  # 上一个阈值
            upper_threshold = ratio_thresholds[i][0]  # 当前阈值
            power_ratio = ratio_thresholds[i - 1][1]  # 上一个阈值对应的副功率比例

            # 如果当前溶液比在两个阈值之间，且大于等于下限小于上限
            if lower_threshold <= max_ratio < upper_threshold:
                print(
                    f"溶液比大于等于 {lower_threshold} 且小于 {upper_threshold}，调整副功率为 {initial_power * power_ratio}")
                self.last_power = initial_power * power_ratio
                return initial_power * power_ratio  # 返回对应副功率

        # 如果最大溶液比大于最后一个阈值
        if max_ratio >= ratio_thresholds[-1][0]:
            power_ratio = ratio_thresholds[-1][1]
            print(f"溶液比大于等于 {ratio_thresholds[-1][0]}，调整副功率为 {initial_power * power_ratio}")
            self.last_power = initial_power * power_ratio
            self.power_zeroed = True
            return initial_power * power_ratio  # 返回对应副功率

        # 如果没有满足条件，返回上一个副功率值
        if self.last_power is not None:
            print(f"没有满足条件，返回上一个副功率 {self.last_power}")
            return self.last_power  # 返回上一个副功率
        else:
            print(f"没有满足条件，副功率设置为初始功率 {initial_power}")
            self.last_power = initial_power
            return initial_power  # 如果没有上一个副功率，返回初始功率

    # 此函数用于计算滑窗内翻料占比的最大值
    def max_turnover_ratio(self, turnover_data, window_size=120):
        """
        计算 turnover_data 数组中，过去 window_size 秒内的最大翻料占比 (turnover_ratio) 值。

        参数：
        turnover_data (list of tuples): 包含时间和翻料占比的元组 [(time, turnover_ratio), ...]
        window_size (int): 时间窗口大小，单位为秒，默认 120秒

        返回：
        float: 滤波后的最大值，如果没有符合条件的数据，返回 None
        """
        if not turnover_data:
            return None  # 如果 turnover_data 为空，直接返回 None

        latest_time = turnover_data[-1][0]
        max_value = float('-inf')

        for time, value in reversed(turnover_data):
            if latest_time - time > window_size:  # 超过时间窗口
                break
            max_value = max(max_value, value)

        # 如果没有符合条件的数据，返回 None 或者其他你认为合适的值
        if max_value == float('-inf'):
            return max(turnover_data, key=lambda x: x[1])

        return max_value

    def min_turnover_ratio(self, ratio_data, window_size=120):
        """
        计算过去120秒内的最小 ratio 值。
        参数：
        ratio_data (list of tuples): 包含时间和比值的元组 [(t, ratio), ...]
        返回：
        float: 过去120秒内 ratio 的最小值
        """
        # 获取当前时间（即最后一个时间点）
        latest_time = ratio_data[-1][0]

        # 筛选出过去120秒内的数据
        last_120_seconds_data = [
            (t, ratio) for t, ratio in reversed(ratio_data)
            if latest_time - t <= window_size
        ]
        # 确保至少有一个数据点
        if not last_120_seconds_data:
            return 0

        # 提取出所有在过去120秒内的数据的 ratio
        ratios = [ratio for t, ratio in last_120_seconds_data]

        # 返回过去120秒内 ratio 的最小值
        min_ratio = min(ratios)

        return min_ratio

    def calculate_slope(self, fime_data):
        """
        计算过去两分钟（120秒）内薄膜占比的拟合直线斜率。

        参数：
        fime_data (list of tuples): 包含时间和薄膜占比的元组 [(t, film_ratio), ...]

        返回：
        float: 过去两分钟内薄膜占比的拟合直线斜率
        """
        # 获取当前时间（即最后一个时间点）
        latest_time = fime_data[-1][0]

        # 选择过去两分钟（120秒）内的数据
        last_two_minutes_data = [
            (t, film_ratio) for t, film_ratio in reversed(fime_data)
            if latest_time - t <= 120
        ]

        # 确保有足够的数据来进行拟合（至少两个数据点）
        if len(last_two_minutes_data) < 2:
            return 0

        # 提取时间和薄膜占比的列表
        times = np.array([t for t, film_ratio in last_two_minutes_data])
        film_ratios = np.array([film_ratio for t, film_ratio in last_two_minutes_data])

        # 使用 np.polyfit 进行线性拟合（一次多项式）
        coefficients = np.polyfit(times, film_ratios, 1)  # 拟合一次多项式（直线）

        # 获取拟合直线的斜率
        slope = coefficients[0]

        return slope

    def calculate_silicon_thermal_energy(self, weight_kg, temperature_celsius):
        """
        计算硅热能需求 (kWh)

        Args:
            weight_kg: 硅重量 (kg)
            temperature_celsius: 温度 (摄氏度)

        Returns:
            float: 硅热能需求 (J)
        """
        if weight_kg <= 0 or temperature_celsius <= 0:
            return 0.0

        # 硅的物理特性常数（基于标准参考值）
        # 固态硅比热容：温度相关，使用分段近似
        # 0-600°C: 约700 J/kg·K
        # 600-1414°C: 约900 J/kg·K

        # 熔点温度
        melting_point = 1414.0  # 摄氏度

        # 熔化潜热
        latent_heat_fusion = 1.8e6  # J/kg (1800 kJ/kg)

        # 液态硅比热容
        liquid_specific_heat = 1000.0  # J/kg·K

        total_energy = 0.0

        if temperature_celsius <= melting_point:
            # 固态加热：分段计算比热容
            if temperature_celsius <= 600:
                # 0-目标温度，使用低温比热容
                total_energy = weight_kg * 700.0 * temperature_celsius
            else:
                # 0-600°C + 600°C-目标温度
                energy_low_temp = weight_kg * 700.0 * 600.0
                energy_high_temp = weight_kg * 900.0 * (temperature_celsius - 600.0)
                total_energy = energy_low_temp + energy_high_temp
        else:
            # 需要完全熔化并继续加热
            # 1. 固态加热到熔点
            energy_to_600 = weight_kg * 700.0 * 600.0  # 0-600°C
            energy_600_to_melting = weight_kg * 900.0 * (melting_point - 600.0)  # 600-1414°C
            energy_to_melting = energy_to_600 + energy_600_to_melting

            # 2. 熔化能量
            melting_energy = weight_kg * latent_heat_fusion

            # 3. 液态加热（熔点以上）
            energy_above_melting = weight_kg * liquid_specific_heat * (temperature_celsius - melting_point)

            total_energy = energy_to_melting + melting_energy + energy_above_melting

        return total_energy

    def _calculate_real_time_vice_power(self, time_interval=None, predicted_total_power=None):
        """
        计算实时副功率值

        使用传入的 time_interval 参数计算副功率累积输出

        Args:
            time_interval: 实际运行时间间隔（秒，float类型），有效范围 [0, 43200]（12小时内）
            predicted_total_power: 预测的累计副功率总量（kWh），必须为正数

        Returns:
            float: 实时副功率值
        """
        try:
            # 如果副功率已经关闭，直接返回0
            if self.vice_power_shutdown:
                return 0

            # 参数验证和处理
            # 验证 predicted_total_power 参数
            if predicted_total_power is not None:
                if not isinstance(predicted_total_power, (int, float)):
                    print(f"警告：预测总功率参数类型无效，期望数值类型，实际: {type(predicted_total_power)}")
                    predicted_total_power = None  # 设为None，跳过比较逻辑
                elif predicted_total_power < 0:
                    print(f"警告：预测总功率不能为负数: {predicted_total_power} kWh，使用绝对值")
                    predicted_total_power = abs(predicted_total_power)
                elif predicted_total_power > 1000:
                    print(f"警告：预测总功率过大: {predicted_total_power} kWh，可能存在异常")

            # 处理 time_interval 参数
            if time_interval is not None:
                # 验证 time_interval 参数类型和范围
                if not isinstance(time_interval, (int, float)):
                    print(f"警告：时间间隔参数类型无效，期望数值类型，实际: {type(time_interval)}")
                    print("保持当前累积值不变")
                    if not hasattr(self, 'vice_power_cumulative'):
                        self.vice_power_cumulative = 0.0
                    return self.real_time_vice_power

                try:
                    # time_interval 是以秒为单位的 float 类型数值
                    time_interval_seconds = float(time_interval)

                    # 验证时间间隔的有效性
                    if time_interval_seconds < 0:
                        print(f"警告：时间间隔为负数，使用绝对值: {time_interval_seconds} 秒")
                        time_interval_seconds = abs(time_interval_seconds)

                    if time_interval_seconds > 43200:  # 超过12小时
                        print(f"警告：时间间隔过大 {time_interval_seconds} 秒（{time_interval_seconds/3600:.1f} 小时），限制为12小时")
                        time_interval_seconds = 43200

                    # 转换为小时
                    time_interval_hours = time_interval_seconds / 3600.0

                    # 如果时间间隔为零，直接返回当前值
                    if time_interval_hours <= 0:
                        return self.real_time_vice_power

                    # 使用固定副功率80kW计算累积输出
                    # 正确的计算方式：固定副功率 × 实际运行时间
                    self.vice_power_cumulative = 80.0 * time_interval_hours

                    print(f"副功率累积计算: 80 kW × {time_interval_hours:.2f} h = {self.vice_power_cumulative:.2f} kWh")

                except (ValueError, TypeError) as e:
                    print(f"时间间隔参数解析失败: {e}, 使用默认逻辑")
                    # 确保 vice_power_cumulative 有初始值
                    if not hasattr(self, 'vice_power_cumulative'):
                        self.vice_power_cumulative = 0.0
                    return self.real_time_vice_power
            else:
                # 如果没有提供 time_interval，保持当前累积值不变
                print("未提供 time_interval 参数，保持当前副功率值和累积值")
                # 确保 vice_power_cumulative 有初始值
                if not hasattr(self, 'vice_power_cumulative'):
                    self.vice_power_cumulative = 0.0
                return self.real_time_vice_power

            # 使用传入的预测累计副功率总量进行比较
            if predicted_total_power is not None:
                # 比较累积输出与预测总功率
                if self.vice_power_cumulative >= predicted_total_power:
                    # 累积输出已达到或超过预测值，永久关闭副功率
                    self.vice_power_shutdown = True
                    self.real_time_vice_power = 0
                    print(f"副功率累积输出 {self.vice_power_cumulative:.2f} kWh 已达到预测总量 {predicted_total_power:.2f} kWh，永久关闭副功率")
                    return 0
                else:
                    # 继续保持副功率
                    print(f"副功率继续运行: 累积输出 {self.vice_power_cumulative:.2f} kWh < 预测总量 {predicted_total_power:.2f} kWh")
                    return self.real_time_vice_power
            else:
                # 如果没有预测值，保持当前副功率值
                print("没有预测总量，保持当前副功率值")
                return self.real_time_vice_power

        except Exception as e:
            print(f"实时副功率计算失败: {e}")
            return self.real_time_vice_power

    def _build_vice_power_info(self, real_time_vice_power, predicted_total_power=None):
        """
        构建副功率信息列表

        Args:
            real_time_vice_power: 实时副功率值
            predicted_total_power: 预测的累计副功率总量（可选）

        Returns:
            list: [real_time_vice_power, current_cumulative_power, predicted_total_power]
        """
        # 确保累积功率值的一致性：如果实时副功率为0且已关闭，累积值应保持最后的值
        current_cumulative_power = getattr(self, 'vice_power_cumulative', 0.0)

        return [real_time_vice_power, current_cumulative_power, predicted_total_power]

    def _predict_vice_power_realtime(self, barrelage, sum_jialiao_time, last_jialiao_weight, ccd, cumulative_feed_weight=None):
        """
        实时预测累计副功率总量 - 包含降级机制

        Args:
            barrelage: 加料桶数
            sum_jialiao_time: 总加料时间 (秒)
            last_jialiao_weight: 最后一次加料重量 (kg)
            ccd: CCD温度
            cumulative_feed_weight: 实时累计加料重量 (kg)，优先使用此值

        Returns:
            float or None: 预测的累计副功率总量 (kWh)，失败时返回None以触发降级机制
        """
        # 检查预测器可用性
        if not self.vice_power_predictor:
            print("副功率预测器不可用，返回None触发降级机制")
            return None

        try:
            # 温度数据预处理：当ccd无效时，尝试使用ccd3或默认值
            effective_ccd = ccd
            if ccd <= 0:
                # 尝试使用实例变量中的ccd3或目标温度
                if hasattr(self, 'target_ccd') and self.target_ccd > 0:
                    effective_ccd = self.target_ccd
                    print(f"CCD温度无效({ccd})，使用目标温度: {effective_ccd}°C")
                else:
                    effective_ccd = 1448  # 使用默认的控温目标
                    print(f"CCD温度无效({ccd})，使用默认温度: {effective_ccd}°C")

            # 计算输入特征（传入实时累计加料重量和修正后的温度）
            features = self._calculate_vice_power_features(effective_ccd, cumulative_feed_weight)

            # 根据投料类型选择对应的预测模型
            process_type = '首投' if self.feeding_type == 0 else '复投'

            # 进行预测
            result = self.vice_power_predictor.predict_single(
                weight_difference=features['weight_difference'],
                silicon_thermal_energy_kwh=features['silicon_thermal_energy_kwh'],
                process_type=process_type
            )

            # 修正字段名：使用正确的返回字段名
            predicted_total = result.get('predicted_vice_power_kwh')  # 使用正确的字段名
            if predicted_total is not None and predicted_total > 0:
                print(f"实时预测累计副功率总量: {predicted_total:.2f} kWh (工艺类型: {process_type})")
                return float(predicted_total)  # 确保返回float类型
            else:
                print(f"预测器返回无效值，触发降级机制。详细结果: {result}")
                if 'error_message' in result:
                    print(f"错误信息: {result.get('error_message', '无')}")
                if 'error_code' in result:
                    print(f"错误代码: {result.get('error_code', '无')}")
                return None  # 返回None触发降级机制

        except Exception as e:
            print(f"实时预测累计副功率失败，触发降级机制: {e}")
            return None  # 返回None触发降级机制

    def _calculate_vice_power_features(self, ccd_temperature=1448, cumulative_feed_weight=None):
        """
        计算副功率预测模型所需的输入特征

        Args:
            ccd_temperature: CCD温度 (摄氏度)，默认1448°C，有效范围 [1000, 2000]
            cumulative_feed_weight: 实时累计加料重量 (kg)，优先使用此值，必须为非负数

        Returns:
            dict: 包含 weight_difference 和 silicon_thermal_energy_kwh 的字典
        """
        try:
            # 参数类型和范围验证
            # 验证 ccd_temperature 参数
            if not isinstance(ccd_temperature, (int, float)):
                print(f"警告：CCD温度参数类型无效，期望数值类型，实际: {type(ccd_temperature)}")
                ccd_temperature = 1448  # 使用默认值

            if not (1000 <= ccd_temperature <= 2000):
                print(f"警告：CCD温度超出推荐范围 [1000, 2000]°C: {ccd_temperature}°C")
                # 限制在有效范围内
                ccd_temperature = max(1000, min(ccd_temperature, 2000))
                print(f"已调整为: {ccd_temperature}°C")

            # 验证 cumulative_feed_weight 参数
            if cumulative_feed_weight is not None:
                if not isinstance(cumulative_feed_weight, (int, float)):
                    print(f"警告：累积加料重量参数类型无效，期望数值类型，实际: {type(cumulative_feed_weight)}")
                    cumulative_feed_weight = None  # 设为None，使用备选逻辑
                elif cumulative_feed_weight < 0:
                    print(f"警告：累积加料重量不能为负数: {cumulative_feed_weight} kg，使用绝对值")
                    cumulative_feed_weight = abs(cumulative_feed_weight)
                elif cumulative_feed_weight > 1000:
                    print(f"警告：累积加料重量过大: {cumulative_feed_weight} kg，可能存在异常")
                    # 不强制限制，但给出警告
            # 计算重量差异 (直接使用累积加料重量，限制在700kg以内)
            if cumulative_feed_weight is not None and cumulative_feed_weight > 0:
                # 直接使用累积加料重量，限制在700kg以内
                weight_difference = min(cumulative_feed_weight, 700)
            elif hasattr(self, 'jialiao') and self.jialiao > 0:
                # 使用总加料量，同样限制在700kg以内
                weight_difference = min(self.jialiao, 700)
            else:
                # 使用默认值
                weight_difference = 150.0

            # 计算硅热能 (使用与 crystal_data_analyzer 相同的物理学计算方式)
            # 使用重量差异的绝对值作为硅重量
            silicon_weight_kg = abs(weight_difference)

            # 使用物理学公式计算硅热能需求
            silicon_thermal_energy_j = self.calculate_silicon_thermal_energy(silicon_weight_kg, ccd_temperature)
            silicon_thermal_energy_kwh = silicon_thermal_energy_j / 3.6e6  # 转换为kWh (1 kWh = 3.6e6 J)

            # 确保数值在合理范围内
            weight_difference = max(50, min(weight_difference, 700))  # 50-700 kg
            silicon_thermal_energy_kwh = max(50, min(silicon_thermal_energy_kwh, 1000))  # 50-1000 kWh

            return {
                'weight_difference': float(weight_difference),
                'silicon_thermal_energy_kwh': float(silicon_thermal_energy_kwh)
            }
        except Exception as e:
            print(f"计算副功率特征失败: {e}")
            # 返回安全的默认值
            return {
                'weight_difference': 150.0,
                'silicon_thermal_energy_kwh': 200.0
            }

    def predict(self, t, ratio, ccd, ccd3, fullmelting, sum_jialiao_time, last_jialiao_time, last_jialiao_weight,
                last_Interval_time, barrelage,
                last_but_one_jialiao_weight, last_but_one_jialiao_time, last_but_one_jialiao_interval_time, film_ratio,
                turnover_ratio, time_interval=None, cumulative_feed_weight=None):
        '''加料信息'''
        if not self.feed_data or not self.last_data or not self.last_but_one_data:
            self.feed_data = [self.jialiao, barrelage, sum_jialiao_time / 60]
            self.last_data = [last_jialiao_weight, last_jialiao_time / 60, last_Interval_time / 60]
            self.last_but_one_data = [last_but_one_jialiao_weight, last_but_one_jialiao_time / 60,
                                      last_but_one_jialiao_interval_time / 60]

        self.realtime_data.append((t, ratio))
        self.fime_data.append((t, film_ratio))
        self.turnover_data.append((t, turnover_ratio))
        self.turnover_data_filter.append((t, self.max_turnover_ratio(self.turnover_data, window_size=60)))
        self.fime_data_filter.append((t, self.max_turnover_ratio(self.fime_data, window_size=60)))
        self.ratio_data.append((t, ratio))
        ratio_delay = self.min_turnover_ratio(self.ratio_data, window_size=60)
        fullmelting_ratio = self.min_turnover_ratio(self.ratio_data, window_size=480)

        print(ratio_delay, ratio)

        t = t / 60  # second -> min

        # 处理只加一桶料的情况
        if int(barrelage) == 1:
            self.vice_close_ratio = self.one_vice_close_ratio

        EKF_ratio = self.ekf.update(ratio)

        if t <= self.begin_t:
            # 实时预测累计副功率总量（包含降级机制）
            predicted_total_power = self._predict_vice_power_realtime(barrelage, sum_jialiao_time, last_jialiao_weight, ccd, cumulative_feed_weight)

            # 检查预测是否失败，实现降级机制
            if predicted_total_power is None:
                print("副功率预测失败，启用降级机制：predicted_total_power 设为 None")

            # 计算实时副功率（即使预测失败也能正常处理）
            real_time_vice_power = self._calculate_real_time_vice_power(time_interval, predicted_total_power)
            vice_power_info = self._build_vice_power_info(real_time_vice_power, predicted_total_power)
            return self.cur_main_power, self.cur_vice_power, vice_power_info

        values = [last_Interval_time / 60, last_jialiao_weight, last_jialiao_time / 60,
                  last_but_one_jialiao_interval_time / 60, last_but_one_jialiao_weight,
                  last_but_one_jialiao_time / 60]

        if self.ladder_vice_power == 1:
            self.cur_vice_power = self.control_vice_power(self.realtime_data, self.ratio_thresholds,
                                                          self.init_vice_power, t * 60, self.vice_lower,
                                                          self.vice_upper)
            if self.cur_vice_power == 0: self.phase, self.vice_closed_time = __class__.VICE_CLOSE2, t
        if self.phase == __class__.INIT:
            '''寻找关底加限制'''
            if not self.dynamic_setting:
                self.dynamic_setting = True
                self.current_data = {'feed_data': [self.jialiao, barrelage, sum_jialiao_time / 60],
                                     'last_data': [last_jialiao_weight, last_jialiao_time, last_Interval_time / 60],
                                     'crystal_power': self.power_yinjing}
                if self.current_data is not None and len(self.history_data) >= 100:
                    self.vice_lower, self.vice_upper = self.find_close_time_range(self.history_data, self.current_data)

            # 翻料
            if self.max_turnover_ratio(self.turnover_data_filter,
                                       window_size=self.turnover_delay[0] * 60) < self.turnover_threshold:
                # 无薄膜料
                if min((self.fime_data_filter[-1][1] / (100 - ratio_delay)) * 100,
                       100) < self.film_threshold or ratio_delay > 50:
                    if ratio_delay >= self.vice_close_ratio[0]:
                        self.phase, self.cur_vice_power, self.vice_half_closed_time = __class__.VICE_CLOSE1, self.init_vice_power / 2, t
                # 有薄膜料
                elif min((self.fime_data_filter[-1][1] / (100 - ratio_delay)) * 100, 100) >= self.film_threshold:
                    if ratio_delay >= self.vice_close_ratio[0] - self.adjust_space:
                        self.phase, self.cur_vice_power, self.vice_half_closed_time = __class__.VICE_CLOSE1, self.init_vice_power / 2, t


        elif self.phase == __class__.VICE_CLOSE1:
            '''关底加限制'''
            if self.vice_lower is not None and t <= self.vice_lower:
                # 实时预测累计副功率总量
                predicted_total_power = self._predict_vice_power_realtime(barrelage, sum_jialiao_time, last_jialiao_weight, ccd, cumulative_feed_weight)
                # 计算实时副功率（第三个返回值）
                real_time_vice_power = self._calculate_real_time_vice_power(time_interval, predicted_total_power)
                vice_power_info = self._build_vice_power_info(real_time_vice_power, predicted_total_power)
                return self.cur_main_power, self.cur_vice_power, vice_power_info
            if self.vice_upper is not None and t >= self.vice_upper:
                self.set_vice_closed_values(t)

            # 提取共用的判断条件
            close_condition = (ratio_delay >= self.vice_close_ratio[1]) or ratio_delay >= 50
            if self.max_turnover_ratio(self.turnover_data_filter, window_size=self.turnover_delay[
                                                                                  1] * 60) < self.turnover_threshold or ratio_delay > 50:
                # 无薄膜料
                if min((self.fime_data_filter[-1][1] / (100 - ratio_delay)) * 100, 100) < self.film_threshold:
                    if close_condition: self.set_vice_closed_values(t)
                # 有薄膜料
                if min((self.fime_data_filter[-1][1] / (100 - ratio_delay)) * 100, 100) >= self.film_threshold:
                    if (ratio_delay >= (self.vice_close_ratio[1] - self.adjust_space)) or ratio_delay >= 50:
                        self.set_vice_closed_values(t)



        elif self.phase in [__class__.VICE_CLOSE2, __class__.VICE_REOPEN]:
            if self.phase == __class__.VICE_REOPEN:
                self.reopen = 1
                self.reopened_ratios.append((t, ratio))
                if (t - self.reopened_ratios[0][0] >= 3 and np.mean(
                        [r_ >= min(70, self.vice_reopen_ratio + 10) for t_, r_ in self.reopened_ratios if
                         t - t_ <= 3]) >= 0.5) or t - self.vice_reopen_time >= self.max_reopen_time:
                    if t - self.vice_reopen_time > 10:
                        self.phase, self.cur_vice_power, self.vice_closed_time = __class__.VICE_CLOSE2, 0, t
            # 主功率增加延时判断
            if self.min_turnover_ratio(self.ratio_data, window_size=self.main_delay*60) >= self.high_ratio and int(barrelage) > 1:
                self.high_time = t

                # '''时间范围参数更新'''
                # if self.history_time_range:
                #     if not self.is_empty(self.history_time_range):
                #         self.time_range = self.history_time_range
                dt, dp = self.time_range[1] - self.time_range[0], self.mid_power - self.min_main_power
                p = (t - self.vice_closed_time - self.time_range[0]) / dt * dp + self.min_main_power
                self.cur_main_power, self.cur_vice_power = np.clip(p, self.down_clip, self.mid_power), 0

                if self.jialiao > 150 and self.ladder_vice_power == 0:
                    if self.vice_closed_time - self.vice_half_closed_time > 10:
                        during_t = self.vice_closed_time - self.vice_half_closed_time
                        self.cur_main_power += (during_t / (self.vice_time_range[1] - self.vice_time_range[0])) * self.down_clip
                        self.cur_main_power = np.clip(self.cur_main_power, self.down_clip, self.mid_power)
                        self.power_change = np.clip(self.cur_main_power, self.down_clip, self.mid_power)
                    else:
                        self.power_change = self.cur_main_power
                else:
                    self.power_change = self.cur_main_power

                if (t - self.vice_closed_time) >= self.time_range[1]:
                    self.cur_main_power = self.init_main_power
                    self.power_change = self.cur_main_power
                elif (t - self.vice_closed_time) <= self.time_range[0]:
                    self.cur_main_power = self.down_clip
                    self.power_change = self.cur_main_power
                # '''历史上下限限定'''
                # if self.limit_range:
                #     if self.limit_range[0] and (t - self.vice_closed_time) <= self.limit_range[0]:
                #         self.cur_main_power = self.down_clip
                #     if self.limit_range[1] and (t - self.vice_closed_time) >= self.limit_range[1]:
                #         self.cur_main_power = self.init_main_power
                self.phase = __class__.ALMOST_DONE

            # 保留原有的全熔判断逻辑作为备用触发条件
            elif (fullmelting_ratio is not None and fullmelting_ratio >= 98) or fullmelting == 1 and int(barrelage) > 1:
                if self.high_time is None:  # 避免重复设置
                    self.high_time = t
                    self.cur_main_power, self.cur_vice_power = self.power_yinjing*1.15, 0
                    self.cur_main_power = np.clip(self.cur_main_power, self.down_clip, self.mid_power)
                    self.power_change = self.cur_main_power
                    self.phase = __class__.ALMOST_DONE

            if ccd > 0:
                self.phase = __class__.ALMOST_DONE
                
            elif (
            not self.reopened) and t - self.vice_closed_time >= self.vice_check_time and self.ladder_vice_power == 0 and ccd < 0 and ratio != 0:
                self.before_reopened_ratios.append((t, ratio))
                if ratio - self.vice_close_ratio[1] <= 10 and int(barrelage) > 1:
                    if (t - self.before_reopened_ratios[0][0] >= 4 and np.mean(
                            [r_ <= (self.vice_close_ratio[1] + 5) for t_, r_ in self.before_reopened_ratios if
                             t - t_ <= 4]) >= 0.4):
                        self.phase, self.cur_vice_power, self.vice_reopen_time, self.vice_reopen_ratio, self.reopened = __class__.VICE_REOPEN, self.vice_reopen_power * 1.2, t, ratio, True
                    if (t - self.before_reopened_ratios[0][0] >= 4 and np.mean(
                            [r_ <= (self.vice_close_ratio[1] + 10) for t_, r_ in self.before_reopened_ratios if
                             t - t_ <= 4]) >= 0.4):
                        self.phase, self.cur_vice_power, self.vice_reopen_time, self.vice_reopen_ratio, self.reopened = __class__.VICE_REOPEN, self.vice_reopen_power, t, ratio, True
        elif self.phase == __class__.ALMOST_DONE:
            self.before_ratios.append((t, ratio))
            if ratio >= self.done_ratio:
                if self.done_time is None:
                    self.done_time = t
                    self.Quanrong = True
                elif t - self.done_time >= 5:  # 5min
                    self.phase = __class__.DONE

            else:
                self.done_time = None
                if self.high_time is not None:
                    if ratio <= self.undo_main_ratio and int(barrelage) > 1:
                        self.cur_main_power = np.clip(self.init_main_power, self.power_change,
                                                      self.init_main_power)
                    if self.Quanrong is True and self.done_time is None and int(barrelage) > 1:
                        self.before_ratios_done.append((t, ratio))
                        if (t - self.before_ratios_done[0][0] >= 4 and np.mean(
                                [r_ <= 94 for t_, r_ in self.before_ratios_done if t - t_ <= 5]) >= 0.5) or (
                                t - self.high_time) >= self.down_time:
                            self.high_time, self.cur_main_power = None, np.clip(self.init_main_power, self.power_change,
                                                                                self.init_main_power)
        elif self.phase == __class__.DONE:
            self.cur_vice_power = 0
            pass
            if fullmelting == 1 and self.total_duration is None:
                self.total_duration = t
            #     if ccd3 < self.melting_ccd3[0]:
            #         self.cur_main_power = np.clip(self.melting_power_k[0] * self.power_yinjing, 30,self.init_main_power)
            #     if ccd3 > self.melting_ccd3[1]:
            #         self.cur_main_power = np.clip(self.melting_power_k[1] * self.power_yinjing, 30,self.init_main_power)
            #     # if ccd3 >= self.melting_ccd3[0] and ccd3 <= self.melting_ccd3[1]:
            #     #     k = (self.melting_power_k[0] - self.melting_power_k[1]) * self.power_yinjing
            #     #     self.cur_main_power = np.clip(self.cur_main_power + (k * (ccd3 - self.melting_ccd3[0])), 30,self.init_main_power)

            if self.high_time is not None and (t - self.high_time) >= self.down_time:
                self.high_time = None
            if ccd > 0:

                # 如果是第一次获取到有效ccd（假设首次是获取到ccd值时才开始计时）
                if self.first_valid_ccd is None:
                    self.first_valid_ccd = ccd  # 记录第一次获取的ccd
                    self.first_valid_time = t  # 记录第一次获取ccd的时间
                # 计算从第一次获取到ccd开始8分钟后的ccd（直接使用时间差）
                if self.first_valid_ccd is not None:
                    time_diff = t - self.first_valid_time  # 计算过去的时间（单位：分钟）

                    if time_diff >= 10 and self.target_tem is None:
                        # 如果距离首次获取的时间超过8分钟
                        self.target_tem = ccd

                self.high_time = None
                if ccd < self.target_ccd:
                    self.cur_main_power = np.clip(self.mid_power, 0, self.mid_power)
                else:
                    self.cur_main_power = np.clip(self.down_clip, 0, self.down_clip)

        # 实时预测累计副功率总量（包含降级机制）
        predicted_total_power = self._predict_vice_power_realtime(barrelage, sum_jialiao_time, last_jialiao_weight, ccd, cumulative_feed_weight)

        # 检查预测是否失败，实现降级机制
        if predicted_total_power is None:
            print("副功率预测失败，启用降级机制：predicted_total_power 设为 None")
            # 降级机制：将预测值设为 None，让系统优雅处理预测失败
            # 这样可以确保系统继续运行，而不会因为预测失败而崩溃

        # 计算实时副功率（第三个返回值）
        # 即使 predicted_total_power 为 None，_calculate_real_time_vice_power 也能正常处理
        real_time_vice_power = self._calculate_real_time_vice_power(time_interval, predicted_total_power)
        vice_power_info = self._build_vice_power_info(real_time_vice_power, predicted_total_power)

        # 返回三个值格式：(main_power, vice_power, [real_time_vice_power, current_cumulative_power, predicted_total_power])
        # 当预测失败时，predicted_total_power 为 None，系统可以优雅地处理这种情况
        return self.cur_main_power, self.cur_vice_power, vice_power_info


class RealTimeEKF:
    def __init__(self, process_variance=1e-5, measurement_variance=0.1, max_increase_rate=0.1, rate_threshold=0.5,
                 upper_limit=None):
        """
        初始化扩展卡尔曼滤波器
        :param process_variance: 过程噪声的方差
        :param measurement_variance: 测量噪声的方差
        :param upper_limit: 滤波结果的上限（可选）
        """
        self.process_variance = process_variance
        self.measurement_variance = measurement_variance

        # 原始噪声值保存
        self.original_process_variance = process_variance
        self.original_measurement_variance = measurement_variance

        self.max_increase_rate = max_increase_rate
        self.rate_threshold = rate_threshold
        self.upper_limit = upper_limit

        self.x_estimate = None  # 当前估计值
        self.p_estimate = None  # 当前估计误差协方差
        self.prev_measurement = None

    def update(self, measurement):
        """
        使用新的测量值更新滤波器状态
        :param measurement: 实时传入的测量值
        :return: 滤波后的估计值
        """
        if self.x_estimate is None:
            # 初始化
            self.x_estimate = measurement
            self.p_estimate = 1.0
        else:
            # # 假设观测方程为对数函数：h(x) = log(1 + x)
            # h = np.log1p(self.x_estimate)
            # h_derivative = 1 / (1 + self.x_estimate)

            # 预测阶段
            x_predict = self.x_estimate
            p_predict = self.p_estimate + self.process_variance

            # 更新阶段
            kalman_gain = p_predict / (p_predict + self.measurement_variance)
            self.x_estimate = x_predict + kalman_gain * (measurement - x_predict)
            self.p_estimate = (1 - kalman_gain) * p_predict

            # 速率检测，判断溶液比是否突增
            if self.prev_measurement is not None:
                rate = measurement - self.prev_measurement  # 用原始测量数据的变化来计算速率
                if rate > self.rate_threshold:  # 溶液比增大超过阈值，判定为突增
                    # print('到这里了吗')
                    # 增大测量噪声，减小滤波器对突增的敏感度
                    self.measurement_variance = min(
                        self.original_measurement_variance + (rate - self.rate_threshold) * 0.2,
                        self.max_increase_rate)
                    self.process_variance = 0.9 * self.original_process_variance
                    # print('更新后的',self.measurement_variance,self.process_variance)
                else:
                    # 恢复正常噪声值
                    self.measurement_variance = self.original_measurement_variance
                    self.process_variance = self.original_process_variance

            # 如果设置了上限，将滤波结果限制在上限以内
            if self.upper_limit is not None:
                self.x_estimate = min(self.x_estimate, self.upper_limit)

        # 更新上一个测量值和估计值
        self.prev_measurement = measurement

        return self.x_estimate