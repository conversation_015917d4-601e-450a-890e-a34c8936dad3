#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拉晶设备运行数据分析脚本
Crystal Equipment Operation Data Analysis Script

功能：
1. 分析多个设备运行文件夹中的CSV数据
2. 提取关键运行参数和计算总能量
3. 生成汇总报告

作者：AI Assistant
日期：2025-01-01
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import logging
from pathlib import Path
import warnings
from feed_type_classifier import FeedTypeClassifier

# 忽略pandas警告
warnings.filterwarnings('ignore')

class CrystalDataAnalyzer:
    """拉晶数据分析器"""
    
    def __init__(self, config_file='config.json'):
        """
        初始化分析器

        Args:
            config_file (str): 配置文件路径
        """
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.results = []
        # 初始化首投/复投分类器
        self.feed_classifier = FeedTypeClassifier(logger=self.logger)
        
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "input_directory": ".",
            "output_directory": "output_results",
            "time_format": "%Y-%m-%d %H:%M:%S",
            "preheating_duration_minutes": 10,
            "required_columns": [
                "cc_time",
                "cc_work_procedure",
                "cc_residue_weight",
                "cc_main_heating_display",
                "cc_vice_heating_set",
                "cc_ccd_liquid_temperature"
            ],
            "target_procedure": "预调温",
            "crystal_seeding_keyword": "引晶",
            "encoding": "utf-8",
            "feed_type_analysis": {
                "enabled": True,
                "feed_number_column": "cc_feed_number",
                "filter_type": "全部"  # 可选值: "首投", "复投", "全部"
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
        else:
            # 创建默认配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=4)
            print(f"已创建默认配置文件: {config_file}")
            
        return default_config
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('crystal_analysis.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def find_csv_files(self, directory):
        """
        查找目录中的所有CSV文件
        
        Args:
            directory (str): 目录路径
            
        Returns:
            list: CSV文件路径列表
        """
        csv_files = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.csv'):
                    csv_files.append(os.path.join(root, file))
        return csv_files
    
    def read_csv_safe(self, file_path):
        """
        安全读取CSV文件
        
        Args:
            file_path (str): CSV文件路径
            
        Returns:
            pd.DataFrame or None: 数据框或None
        """
        try:
            # 尝试不同的编码
            encodings = [self.config['encoding'], 'gbk', 'gb2312', 'utf-8-sig']
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    self.logger.info(f"成功读取文件: {file_path} (编码: {encoding})")
                    return df
                except UnicodeDecodeError:
                    continue
                    
            self.logger.error(f"无法读取文件 {file_path}: 编码问题")
            return None
            
        except Exception as e:
            self.logger.error(f"读取文件 {file_path} 失败: {e}")
            return None
    
    def validate_dataframe(self, df, file_path):
        """
        验证数据框是否包含必需的列
        
        Args:
            df (pd.DataFrame): 数据框
            file_path (str): 文件路径
            
        Returns:
            bool: 是否有效
        """
        if df is None or df.empty:
            self.logger.warning(f"文件 {file_path} 为空")
            return False
            
        missing_columns = [col for col in self.config['required_columns'] 
                          if col not in df.columns]
        
        if missing_columns:
            self.logger.warning(f"文件 {file_path} 缺少必需列: {missing_columns}")
            return False
            
        return True
    
    def parse_time(self, time_str):
        """
        解析时间字符串
        
        Args:
            time_str (str): 时间字符串
            
        Returns:
            datetime or None: 解析后的时间
        """
        try:
            return pd.to_datetime(time_str, format=self.config['time_format'])
        except:
            try:
                return pd.to_datetime(time_str)
            except:
                return None
    
    def calculate_time_intervals(self, df):
        """
        计算时间间隔（秒）
        
        Args:
            df (pd.DataFrame): 数据框
            
        Returns:
            pd.Series: 时间间隔序列
        """
        df['parsed_time'] = df['cc_time'].apply(self.parse_time)
        df = df.dropna(subset=['parsed_time'])
        
        if len(df) < 2:
            return pd.Series([0] * len(df))
            
        time_diffs = df['parsed_time'].diff().dt.total_seconds()
        time_diffs.iloc[0] = 0  # 第一个记录的时间间隔设为0
        
        return time_diffs

    def calculate_silicon_thermal_energy(self, weight_kg, temperature_celsius):
        """
        计算多晶硅从0度到指定温度所需的总能量
        基于硅的物理特性和相变过程

        Args:
            weight_kg (float): 硅的重量（公斤）
            temperature_celsius (float): 目标温度（摄氏度）

        Returns:
            float: 所需总能量（焦耳）
        """
        if weight_kg <= 0 or temperature_celsius <= 0:
            return 0.0

        # 硅的物理特性常数（基于标准参考值）
        # 固态硅比热容：温度相关，使用分段近似
        # 0-600°C: 约700 J/kg·K
        # 600-1414°C: 约900 J/kg·K

        # 熔点温度
        melting_point = 1414.0  # 摄氏度

        # 熔化潜热
        latent_heat_fusion = 1.8e6  # J/kg (1800 kJ/kg)

        # 液态硅比热容
        liquid_specific_heat = 1000.0  # J/kg·K

        total_energy = 0.0

        if temperature_celsius <= melting_point:
            # 固态加热：分段计算比热容
            if temperature_celsius <= 600:
                # 0-目标温度，使用低温比热容
                total_energy = weight_kg * 700.0 * temperature_celsius
            else:
                # 0-600°C + 600°C-目标温度
                energy_low_temp = weight_kg * 700.0 * 600.0
                energy_high_temp = weight_kg * 900.0 * (temperature_celsius - 600.0)
                total_energy = energy_low_temp + energy_high_temp
        else:
            # 需要完全熔化并继续加热
            # 1. 固态加热到熔点
            energy_to_600 = weight_kg * 700.0 * 600.0  # 0-600°C
            energy_600_to_melting = weight_kg * 900.0 * (melting_point - 600.0)  # 600-1414°C
            energy_to_melting = energy_to_600 + energy_600_to_melting

            # 2. 熔化能量
            melting_energy = weight_kg * latent_heat_fusion

            # 3. 液态加热（熔点以上）
            energy_above_melting = weight_kg * liquid_specific_heat * (temperature_celsius - melting_point)

            total_energy = energy_to_melting + melting_energy + energy_above_melting

        return total_energy
    
    def find_first_crystal_seeding_power(self, df):
        """
        查找第一次出现"引晶"状态时的主功率数值

        Args:
            df (pd.DataFrame): 数据框

        Returns:
            float or None: 引晶时的主功率值(kW)，如果未找到则返回None
        """
        try:
            # 从配置文件获取引晶关键词
            crystal_seeding_keyword = self.config.get('crystal_seeding_keyword', '引晶')

            # 查找包含引晶关键词的记录
            crystal_seeding_records = df[df['cc_work_procedure'].str.contains(crystal_seeding_keyword, na=False)]

            if crystal_seeding_records.empty:
                self.logger.warning(f"未找到包含'{crystal_seeding_keyword}'关键词的记录")
                return None

            # 获取第一次出现引晶状态的记录
            first_seeding_record = crystal_seeding_records.iloc[0]

            # 提取主功率数值
            main_power = first_seeding_record['cc_main_heating_display']

            # 验证数值有效性
            if pd.isna(main_power) or not isinstance(main_power, (int, float)):
                self.logger.warning(f"{crystal_seeding_keyword}时的主功率数值无效")
                return None

            self.logger.info(f"找到{crystal_seeding_keyword}时主功率: {main_power} kW")
            return float(main_power)

        except Exception as e:
            self.logger.error(f"查找{crystal_seeding_keyword}主功率时出错: {e}")
            return None

    def analyze_feed_type(self, df):
        """
        分析首投/复投类型
        判断逻辑：如果数据文件中cc_feed_number字段有任何记录等于1，则整个文件标记为"首投"，否则为"复投"

        Args:
            df (pd.DataFrame): 数据框

        Returns:
            tuple: (feed_type, feed_type_stats) 首投/复投类型和统计信息
        """
        try:
            # 检查是否启用首投/复投分析
            if not self.config.get('feed_type_analysis', {}).get('enabled', True):
                return "未分析", {}

            feed_number_column = self.config.get('feed_type_analysis', {}).get('feed_number_column', 'cc_feed_number')

            # 检查是否存在cc_feed_number列
            if feed_number_column not in df.columns:
                self.logger.warning(f"数据中未找到{feed_number_column}列，无法进行首投/复投分析")
                return "未知", {}

            # 检查是否有任何记录的cc_feed_number等于1
            has_feed_number_1 = False
            unique_values = df[feed_number_column].unique()

            for value in unique_values:
                if not pd.isna(value):
                    try:
                        if float(value) == 1.0:
                            has_feed_number_1 = True
                            break
                    except (ValueError, TypeError):
                        # 尝试字符串比较
                        if str(value).strip() in ["1", "1.0"]:
                            has_feed_number_1 = True
                            break

            # 根据是否包含等于1的值来判断整个文件的类型
            if has_feed_number_1:
                file_feed_type = "首投"
            else:
                file_feed_type = "复投"

            # 统计信息
            total_records = len(df)
            records_with_1 = 0

            if has_feed_number_1:
                # 计算等于1的记录数
                for value in df[feed_number_column]:
                    if not pd.isna(value):
                        try:
                            if float(value) == 1.0:
                                records_with_1 += 1
                        except (ValueError, TypeError):
                            if str(value).strip() in ["1", "1.0"]:
                                records_with_1 += 1

            # 转换numpy类型为Python原生类型，避免JSON序列化问题
            clean_unique_values = []
            for v in unique_values:
                if not pd.isna(v):
                    try:
                        # 尝试转换为Python int或float
                        if isinstance(v, (int, float)):
                            clean_unique_values.append(int(v) if v == int(v) else float(v))
                        else:
                            clean_unique_values.append(str(v))
                    except:
                        clean_unique_values.append(str(v))

            stats = {
                "文件类型": file_feed_type,
                "总记录数": total_records,
                "cc_feed_number等于1的记录数": records_with_1,
                "唯一cc_feed_number值": sorted(clean_unique_values),
                "包含等于1的值": has_feed_number_1
            }

            self.logger.info(f"首投/复投分析完成 - 文件类型: {file_feed_type}, "
                           f"总记录数: {total_records}, "
                           f"等于1的记录数: {records_with_1}, "
                           f"唯一值: {stats['唯一cc_feed_number值']}")

            return file_feed_type, stats

        except Exception as e:
            self.logger.error(f"首投/复投分析时出错: {e}")
            return "未知", {}



    def analyze_single_csv(self, file_path):
        """
        分析单个CSV文件

        Args:
            file_path (str): CSV文件路径

        Returns:
            dict or None: 分析结果
        """
        df = self.read_csv_safe(file_path)

        if not self.validate_dataframe(df, file_path):
            return None

        try:
            # 解析时间并排序
            df['parsed_time'] = df['cc_time'].apply(self.parse_time)
            df = df.dropna(subset=['parsed_time']).sort_values('parsed_time')

            if len(df) == 0:
                self.logger.warning(f"文件 {file_path} 没有有效的时间数据")
                return None

            # 1. 运行开始时间：第一条记录的时间
            start_time = df.iloc[0]['parsed_time']

            # 2. 查找"预调温"状态
            preheating_records = df[df['cc_work_procedure'] == self.config['target_procedure']]

            if preheating_records.empty:
                self.logger.warning(f"文件 {file_path} 中未找到'{self.config['target_procedure']}'状态")
                # 使用最后一条记录作为结束时间
                end_time = df.iloc[-1]['parsed_time']
            else:
                # 使用第一个"预调温"记录的时间加上10分钟
                preheating_start = preheating_records.iloc[0]['parsed_time']
                end_time = preheating_start + timedelta(minutes=self.config['preheating_duration_minutes'])

            # 3. 获取开始和结束时的重量
            start_weight = df.iloc[0]['cc_residue_weight']

            # 找到最接近结束时间的记录
            df['time_diff'] = abs((df['parsed_time'] - end_time).dt.total_seconds())
            end_record = df.loc[df['time_diff'].idxmin()]
            end_weight = end_record['cc_residue_weight']
            end_temperature = end_record['cc_ccd_liquid_temperature']

            # 4. 重量差值
            weight_difference = end_weight - start_weight

            # 5. 查找引晶时的主功率（新增功能）
            first_crystal_seeding_main_power = self.find_first_crystal_seeding_power(df)

            # 6. 分析首投/复投类型（新增功能）
            feed_type, feed_type_stats = self.analyze_feed_type(df)

            # 6. 计算能量（功率 × 时间）
            # 计算时间间隔
            time_intervals = self.calculate_time_intervals(df)

            # 主功率总能量 (kWh)
            df['main_power_energy'] = (df['cc_main_heating_display'] * time_intervals / 3600)
            main_total_energy = df['main_power_energy'].sum()

            # 副功率总能量 (kWh)
            df['vice_power_energy'] = (df['cc_vice_heating_set'] * time_intervals / 3600)
            vice_total_energy = df['vice_power_energy'].sum()

            # 7. 计算硅的理论热能需求
            # 重量差值单位已经是公斤
            silicon_weight_kg = abs(weight_difference)  # 单位：kg
            silicon_thermal_energy_j = self.calculate_silicon_thermal_energy(silicon_weight_kg, end_temperature)
            silicon_thermal_energy_kwh = silicon_thermal_energy_j / 3.6e6  # 转换为kWh

            result = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'folder_name': os.path.basename(os.path.dirname(file_path)),
                'start_time': start_time.strftime(self.config['time_format']),
                'end_time': end_time.strftime(self.config['time_format']),
                'start_weight': start_weight,
                'end_weight': end_weight,
                'weight_difference': weight_difference,
                'end_temperature_celsius': end_temperature,
                'first_crystal_seeding_main_power_kw': first_crystal_seeding_main_power if first_crystal_seeding_main_power is not None else -1,
                'feed_type': feed_type,  # 新增：首投/复投类型
                'cc_feed_number_values': str(feed_type_stats.get('唯一cc_feed_number值', [])),  # 新增：cc_feed_number唯一值
                'feed_number_1_records': feed_type_stats.get('cc_feed_number等于1的记录数', 0),  # 新增：等于1的记录数
                'main_total_energy_kwh': main_total_energy,
                'vice_total_energy_kwh': vice_total_energy,
                'total_energy_kwh': main_total_energy + vice_total_energy,
                'silicon_thermal_energy_kwh': silicon_thermal_energy_kwh,
                'energy_efficiency_percent': (silicon_thermal_energy_kwh / (main_total_energy + vice_total_energy) * 100) if (main_total_energy + vice_total_energy) > 0 else 0,
                'record_count': len(df),
                'duration_hours': (end_time - start_time).total_seconds() / 3600,
                'preheating_found': not preheating_records.empty
            }
            
            self.logger.info(f"成功分析文件: {file_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"分析文件 {file_path} 时出错: {e}")
            return None
    
    def analyze_folder(self, folder_path):
        """
        分析文件夹中的所有CSV文件
        
        Args:
            folder_path (str): 文件夹路径
            
        Returns:
            list: 分析结果列表
        """
        csv_files = self.find_csv_files(folder_path)
        folder_results = []
        
        self.logger.info(f"在文件夹 {folder_path} 中找到 {len(csv_files)} 个CSV文件")
        
        for csv_file in csv_files:
            result = self.analyze_single_csv(csv_file)
            if result:
                folder_results.append(result)
        
        return folder_results

    def filter_results_by_feed_type(self, results, filter_type="全部"):
        """
        根据首投/复投类型筛选结果

        Args:
            results (list): 分析结果列表
            filter_type (str): 筛选类型 ("首投", "复投", "全部")

        Returns:
            list: 筛选后的结果列表
        """
        if filter_type == "全部":
            return results

        filtered_results = [r for r in results if r.get('feed_type') == filter_type]

        self.logger.info(f"筛选完成 - 类型: {filter_type}, "
                       f"筛选前: {len(results)}个文件, 筛选后: {len(filtered_results)}个文件")

        return filtered_results

    def get_feed_type_summary(self, results):
        """
        获取首投/复投类型的详细统计摘要

        Args:
            results (list): 分析结果列表

        Returns:
            dict: 详细统计信息
        """
        if not results:
            return {}

        # 基础统计
        total_files = len(results)
        first_feed_results = [r for r in results if r.get('feed_type') == '首投']
        repeat_feed_results = [r for r in results if r.get('feed_type') == '复投']
        unknown_results = [r for r in results if r.get('feed_type') not in ['首投', '复投']]

        # 能效统计
        first_efficiencies = [r.get('energy_efficiency_percent', 0) for r in first_feed_results if r.get('energy_efficiency_percent', 0) > 0]
        repeat_efficiencies = [r.get('energy_efficiency_percent', 0) for r in repeat_feed_results if r.get('energy_efficiency_percent', 0) > 0]

        # 能量统计
        first_energies = [r.get('total_energy_kwh', 0) for r in first_feed_results]
        repeat_energies = [r.get('total_energy_kwh', 0) for r in repeat_feed_results]

        # 运行时长统计
        first_durations = [r.get('duration_hours', 0) for r in first_feed_results]
        repeat_durations = [r.get('duration_hours', 0) for r in repeat_feed_results]

        summary = {
            "基础统计": {
                "总文件数": total_files,
                "首投文件数": len(first_feed_results),
                "复投文件数": len(repeat_feed_results),
                "未知类型文件数": len(unknown_results),
                "首投比例(%)": (len(first_feed_results) / total_files * 100) if total_files > 0 else 0,
                "复投比例(%)": (len(repeat_feed_results) / total_files * 100) if total_files > 0 else 0
            },
            "能效对比": {
                "首投平均能效(%)": sum(first_efficiencies) / len(first_efficiencies) if first_efficiencies else 0,
                "复投平均能效(%)": sum(repeat_efficiencies) / len(repeat_efficiencies) if repeat_efficiencies else 0,
                "首投最高能效(%)": max(first_efficiencies) if first_efficiencies else 0,
                "复投最高能效(%)": max(repeat_efficiencies) if repeat_efficiencies else 0,
                "首投最低能效(%)": min(first_efficiencies) if first_efficiencies else 0,
                "复投最低能效(%)": min(repeat_efficiencies) if repeat_efficiencies else 0
            },
            "能量对比": {
                "首投平均总能量(kWh)": sum(first_energies) / len(first_energies) if first_energies else 0,
                "复投平均总能量(kWh)": sum(repeat_energies) / len(repeat_energies) if repeat_energies else 0,
                "首投总能量消耗(kWh)": sum(first_energies),
                "复投总能量消耗(kWh)": sum(repeat_energies)
            },
            "时长对比": {
                "首投平均运行时长(小时)": sum(first_durations) / len(first_durations) if first_durations else 0,
                "复投平均运行时长(小时)": sum(repeat_durations) / len(repeat_durations) if repeat_durations else 0,
                "首投最长运行时长(小时)": max(first_durations) if first_durations else 0,
                "复投最长运行时长(小时)": max(repeat_durations) if repeat_durations else 0
            }
        }

        return summary
    
    def save_results_to_csv(self, results, output_file):
        """
        将结果保存为CSV文件

        Args:
            results (list): 分析结果列表
            output_file (str): 输出文件路径
        """
        if not results:
            self.logger.warning("没有结果可保存")
            return

        df_results = pd.DataFrame(results)

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 尝试保存文件，如果失败则尝试备用文件名
        max_attempts = 5
        for attempt in range(max_attempts):
            try:
                if attempt == 0:
                    current_file = output_file
                else:
                    # 生成备用文件名
                    base_name, ext = os.path.splitext(output_file)
                    current_file = f"{base_name}_backup_{attempt}{ext}"

                df_results.to_csv(current_file, index=False, encoding='utf-8-sig')
                self.logger.info(f"结果已保存到: {current_file}")
                return

            except PermissionError as e:
                if attempt < max_attempts - 1:
                    self.logger.warning(f"文件 {current_file} 被占用，尝试备用文件名...")
                    continue
                else:
                    self.logger.error(f"无法保存文件 {output_file}: {e}")
                    self.logger.error("请确保:")
                    self.logger.error("1. 关闭所有打开的CSV文件（如Excel）")
                    self.logger.error("2. 检查文件夹写入权限")
                    self.logger.error("3. 以管理员身份运行脚本")
                    raise
            except Exception as e:
                self.logger.error(f"保存文件 {output_file} 时出错: {e}")
                raise
    
    def run_analysis(self):
        """运行完整分析"""
        self.logger.info("开始拉晶数据分析...")
        
        input_dir = self.config['input_directory']
        output_dir = self.config['output_directory']
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取所有子文件夹
        subfolders = [f for f in os.listdir(input_dir) 
                     if os.path.isdir(os.path.join(input_dir, f))]
        
        if not subfolders:
            self.logger.warning(f"在 {input_dir} 中未找到子文件夹")
            return
        
        all_results = []
        
        for folder in subfolders:
            folder_path = os.path.join(input_dir, folder)
            self.logger.info(f"分析文件夹: {folder}")
            
            folder_results = self.analyze_folder(folder_path)
            
            if folder_results:
                # 为每个文件夹保存单独的结果文件
                folder_output_file = os.path.join(output_dir, f"{folder}_analysis.csv")
                self.save_results_to_csv(folder_results, folder_output_file)
                
                all_results.extend(folder_results)
        
        # 保存所有结果的汇总文件
        if all_results:
            summary_file = os.path.join(output_dir, "all_folders_summary.csv")
            self.save_results_to_csv(all_results, summary_file)
            self.logger.info(f"已保存完整分析结果到: {summary_file}")

            # 获取首投/复投详细统计
            feed_summary = self.get_feed_type_summary(all_results)

            # 根据配置筛选结果
            filter_type = self.config.get('feed_type_analysis', {}).get('filter_type', '全部')
            if filter_type != '全部':
                filtered_results = self.filter_results_by_feed_type(all_results, filter_type)
                if filtered_results:
                    filtered_summary_file = os.path.join(output_dir, f"filtered_{filter_type}_summary.csv")
                    self.save_results_to_csv(filtered_results, filtered_summary_file)
                    self.logger.info(f"已保存{filter_type}筛选结果到: {filtered_summary_file}")
                else:
                    self.logger.warning(f"筛选类型'{filter_type}'没有找到匹配的数据")

            # 自动生成所有类型的筛选文件（如果数据存在）
            if self.config.get('feed_type_analysis', {}).get('enabled', True):
                for auto_filter_type in ['首投', '复投']:
                    auto_filtered_results = self.filter_results_by_feed_type(all_results, auto_filter_type)
                    if auto_filtered_results:
                        auto_filtered_file = os.path.join(output_dir, f"auto_filtered_{auto_filter_type}_summary.csv")
                        self.save_results_to_csv(auto_filtered_results, auto_filtered_file)
                        self.logger.info(f"已自动生成{auto_filter_type}数据文件: {auto_filtered_file}")

            # 生成统计报告（包含首投/复投详细对比）
            self.generate_summary_report(all_results, output_dir, feed_summary)
        
        self.logger.info("分析完成!")

    def generate_summary_report(self, results, output_dir, feed_summary=None):
        """
        生成统计报告

        Args:
            results (list): 分析结果列表
            output_dir (str): 输出目录
            feed_summary (dict): 首投/复投详细统计信息
        """
        if not results:
            return

        df = pd.DataFrame(results)

        # 生成统计报告
        report = {
            "分析概况": {
                "总文件数": len(results),
                "成功分析文件数": len([r for r in results if r.get('preheating_found', False)]),
                "找到引晶数据文件数": len([r for r in results if r.get('first_crystal_seeding_main_power_kw', -1) != -1]),
                "分析时间": datetime.now().strftime(self.config['time_format'])
            },
            "首投/复投详细统计": feed_summary if feed_summary else {
                "首投文件数": len([r for r in results if r.get('feed_type') == '首投']),
                "复投文件数": len([r for r in results if r.get('feed_type') == '复投']),
                "未知类型文件数": len([r for r in results if r.get('feed_type') not in ['首投', '复投']]),
                "首投文件比例(%)": (len([r for r in results if r.get('feed_type') == '首投']) / len(results) * 100) if len(results) > 0 else 0,
                "复投文件比例(%)": (len([r for r in results if r.get('feed_type') == '复投']) / len(results) * 100) if len(results) > 0 else 0
            },
            "重量统计": {
                "平均开始重量": df['start_weight'].mean(),
                "平均结束重量": df['end_weight'].mean(),
                "平均重量差值": df['weight_difference'].mean(),
                "重量差值标准差": df['weight_difference'].std()
            },
            "能量统计": {
                "平均主功率总能量(kWh)": df['main_total_energy_kwh'].mean(),
                "平均副功率总能量(kWh)": df['vice_total_energy_kwh'].mean(),
                "平均总能量(kWh)": df['total_energy_kwh'].mean(),
                "总能量标准差(kWh)": df['total_energy_kwh'].std(),
                "平均硅理论热能需求(kWh)": df['silicon_thermal_energy_kwh'].mean(),
                "平均能量效率(%)": df['energy_efficiency_percent'].mean()
            },
            "引晶功率统计": {
                "平均引晶主功率(kW)": df[df['first_crystal_seeding_main_power_kw'] != -1]['first_crystal_seeding_main_power_kw'].mean() if len(df[df['first_crystal_seeding_main_power_kw'] != -1]) > 0 else 0,
                "最高引晶主功率(kW)": df[df['first_crystal_seeding_main_power_kw'] != -1]['first_crystal_seeding_main_power_kw'].max() if len(df[df['first_crystal_seeding_main_power_kw'] != -1]) > 0 else 0,
                "最低引晶主功率(kW)": df[df['first_crystal_seeding_main_power_kw'] != -1]['first_crystal_seeding_main_power_kw'].min() if len(df[df['first_crystal_seeding_main_power_kw'] != -1]) > 0 else 0,
                "引晶主功率标准差(kW)": df[df['first_crystal_seeding_main_power_kw'] != -1]['first_crystal_seeding_main_power_kw'].std() if len(df[df['first_crystal_seeding_main_power_kw'] != -1]) > 0 else 0
            },
            "温度统计": {
                "平均结束温度(°C)": df['end_temperature_celsius'].mean(),
                "最高结束温度(°C)": df['end_temperature_celsius'].max(),
                "最低结束温度(°C)": df['end_temperature_celsius'].min(),
                "结束温度标准差(°C)": df['end_temperature_celsius'].std()
            },
            "时间统计": {
                "平均运行时长(小时)": df['duration_hours'].mean(),
                "最短运行时长(小时)": df['duration_hours'].min(),
                "最长运行时长(小时)": df['duration_hours'].max()
            }
        }

        # 保存统计报告
        report_file = os.path.join(output_dir, "analysis_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=4, default=str)

        self.logger.info(f"统计报告已保存到: {report_file}")

        # 生成文本格式的报告
        text_report_file = os.path.join(output_dir, "analysis_report.txt")
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write("拉晶设备运行数据分析报告\n")
            f.write("=" * 50 + "\n\n")

            for section, data in report.items():
                f.write(f"{section}:\n")
                f.write("-" * 30 + "\n")
                for key, value in data.items():
                    if isinstance(value, float):
                        f.write(f"{key}: {value:.2f}\n")
                    else:
                        f.write(f"{key}: {value}\n")
                f.write("\n")

        self.logger.info(f"文本报告已保存到: {text_report_file}")

def main():
    """主函数"""
    print("拉晶设备运行数据分析脚本")
    print("=" * 40)

    analyzer = CrystalDataAnalyzer()
    analyzer.run_analysis()

    print("\n分析完成！请查看output_results文件夹中的结果文件。")

if __name__ == "__main__":
    main()
