#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
首投/复投功能测试脚本
Test Script for Feed Type Functionality

功能：
1. 测试首投/复投分类逻辑
2. 测试数据分析功能
3. 验证筛选功能
4. 生成测试报告

作者：AI Assistant
日期：2025-01-07
"""

import os
import json
import pandas as pd
from crystal_data_analyzer import CrystalDataAnalyzer
from feed_type_classifier import FeedTypeClassifier


def test_feed_type_classifier():
    """测试首投/复投分类器"""
    print("🧪 测试首投/复投分类器")
    print("-" * 40)
    
    classifier = FeedTypeClassifier()
    
    # 测试各种cc_feed_number值
    test_cases = [
        (0, "复投"),
        (1, "首投"),
        (2, "复投"),
        (7, "复投"),
        (8, "复投"),
        (10, "复投"),
        (11, "复投"),
        (21, "复投"),
        (1.0, "首投"),
        ("1", "首投"),
        ("1.0", "首投"),
        ("11", "复投"),
        ("21", "复投"),
    ]
    
    all_passed = True
    for value, expected in test_cases:
        result = classifier.classify_feed_type(value)
        status = "✅" if result == expected else "❌"
        print(f"{status} {value} -> {result} (期望: {expected})")
        if result != expected:
            all_passed = False
    
    print(f"\n分类器测试结果: {'✅ 全部通过' if all_passed else '❌ 有失败'}")
    return all_passed


def test_real_data_analysis():
    """测试真实数据分析"""
    print("\n🔬 测试真实数据分析")
    print("-" * 40)
    
    # 创建测试配置
    config = {
        "input_directory": ".",
        "output_directory": "test_output",
        "time_format": "%Y-%m-%d %H:%M:%S",
        "preheating_duration_minutes": 10,
        "required_columns": [
            "cc_time",
            "cc_work_procedure",
            "cc_residue_weight",
            "cc_main_heating_display",
            "cc_vice_heating_set",
            "cc_ccd_liquid_temperature"
        ],
        "target_procedure": "预调温",
        "crystal_seeding_keyword": "引晶",
        "encoding": "utf-8",
        "feed_type_analysis": {
            "enabled": True,
            "feed_number_column": "cc_feed_number",
            "filter_type": "全部"
        }
    }
    
    config_file = "test_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    
    try:
        analyzer = CrystalDataAnalyzer(config_file)
        
        # 测试几个具体文件
        test_files = [
            "analoga01/extracted_content_1.csv",  # 应该是复投
            "analoga02/extracted_content_1.csv",  # 应该是首投
            "analoga03/extracted_content_1.csv",  # 应该是首投
        ]
        
        results = []
        for file_path in test_files:
            if os.path.exists(file_path):
                print(f"\n📁 分析文件: {file_path}")
                result = analyzer.analyze_single_csv(file_path)
                if result:
                    feed_type = result['feed_type']
                    feed_values = result['cc_feed_number_values']
                    feed_1_records = result['feed_number_1_records']
                    total_records = result['record_count']
                    efficiency = result['energy_efficiency_percent']
                    
                    print(f"  类型: {feed_type}")
                    print(f"  cc_feed_number值: {feed_values}")
                    print(f"  等于1的记录数: {feed_1_records}")
                    print(f"  总记录数: {total_records}")
                    print(f"  能效: {efficiency:.2f}%")
                    
                    results.append(result)
                else:
                    print(f"  ❌ 分析失败")
            else:
                print(f"  ❌ 文件不存在: {file_path}")
        
        # 统计结果
        if results:
            first_feed_count = len([r for r in results if r['feed_type'] == '首投'])
            repeat_feed_count = len([r for r in results if r['feed_type'] == '复投'])
            
            print(f"\n📊 测试结果统计:")
            print(f"  总文件数: {len(results)}")
            print(f"  首投文件: {first_feed_count}")
            print(f"  复投文件: {repeat_feed_count}")
            
            return True
        else:
            print("❌ 没有成功分析的文件")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        # 清理测试文件
        try:
            os.remove(config_file)
        except:
            pass


def test_filter_functionality():
    """测试筛选功能"""
    print("\n🔍 测试筛选功能")
    print("-" * 40)
    
    # 创建模拟数据
    test_results = [
        {'feed_type': '首投', 'folder_name': 'analoga01', 'energy_efficiency_percent': 85.5},
        {'feed_type': '复投', 'folder_name': 'analoga02', 'energy_efficiency_percent': 82.3},
        {'feed_type': '首投', 'folder_name': 'analoga03', 'energy_efficiency_percent': 87.1},
        {'feed_type': '复投', 'folder_name': 'analoga04', 'energy_efficiency_percent': 83.7},
        {'feed_type': '首投', 'folder_name': 'analoga05', 'energy_efficiency_percent': 86.2},
    ]
    
    # 创建临时分析器
    config = {"feed_type_analysis": {"enabled": True}}
    analyzer = CrystalDataAnalyzer()
    analyzer.config = config
    
    # 测试筛选
    all_results = analyzer.filter_results_by_feed_type(test_results, "全部")
    first_results = analyzer.filter_results_by_feed_type(test_results, "首投")
    repeat_results = analyzer.filter_results_by_feed_type(test_results, "复投")
    
    print(f"全部数据: {len(all_results)}个文件")
    print(f"首投数据: {len(first_results)}个文件")
    print(f"复投数据: {len(repeat_results)}个文件")
    
    # 验证筛选结果
    expected_first = 3
    expected_repeat = 2
    
    success = (len(all_results) == 5 and 
              len(first_results) == expected_first and 
              len(repeat_results) == expected_repeat)
    
    print(f"筛选功能测试: {'✅ 通过' if success else '❌ 失败'}")
    return success


def test_cc_feed_number_detection():
    """测试cc_feed_number字段检测"""
    print("\n🔍 测试cc_feed_number字段检测")
    print("-" * 40)
    
    # 检查几个实际文件的cc_feed_number字段
    test_files = [
        "analoga01/extracted_content_1.csv",
        "analoga02/extracted_content_1.csv",
        "analoga03/extracted_content_1.csv",
        "analoga11/extracted_content_1.csv",
        "analoga21/extracted_content_1.csv",
    ]
    
    classifier = FeedTypeClassifier()
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                if 'cc_feed_number' in df.columns:
                    unique_values = sorted(df['cc_feed_number'].unique())
                    
                    # 检查是否有等于1的值
                    has_1 = any(classifier.classify_feed_type(v) == "首投" for v in unique_values)
                    file_type = "首投" if has_1 else "复投"
                    
                    print(f"📁 {os.path.basename(os.path.dirname(file_path))}: {unique_values} -> {file_type}")
                else:
                    print(f"❌ {file_path}: 没有cc_feed_number字段")
            except Exception as e:
                print(f"❌ {file_path}: 读取失败 - {e}")
        else:
            print(f"❌ {file_path}: 文件不存在")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始首投/复投功能全面测试")
    print("=" * 50)
    
    tests = [
        ("分类器逻辑测试", test_feed_type_classifier),
        ("真实数据分析测试", test_real_data_analysis),
        ("筛选功能测试", test_filter_functionality),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 额外测试
    test_cc_feed_number_detection()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！首投/复投功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total


if __name__ == "__main__":
    run_all_tests()
