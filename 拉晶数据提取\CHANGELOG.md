# 拉晶数据分析系统更新日志

## [v2.1.0] - 2025-01-01

### 新增功能 ✨

#### 引晶功率提取功能
- **新增字段**: `first_crystal_seeding_main_power_kw` - 记录第一次出现"引晶"状态时的主功率值
- **筛选条件**: 在 `cc_work_procedure` 字段中查找包含"引晶"关键词的记录
- **时间定位**: 自动找到第一次出现"引晶"状态的时间点
- **功率提取**: 获取该时间点对应的 `cc_main_heating_display` 主功率数值
- **单位**: kW（千瓦）
- **异常处理**: 未找到引晶状态时字段值设为 `-1`

#### 配置文件增强
- **新增配置项**: `crystal_seeding_keyword` - 可自定义引晶关键词（默认："引晶"）
- **灵活配置**: 支持根据实际工艺流程名称调整关键词

#### 统计报告增强
- **新增统计部分**: "引晶功率统计"
  - 平均引晶主功率(kW)
  - 最高引晶主功率(kW)
  - 最低引晶主功率(kW)
  - 引晶主功率标准差(kW)
- **分析概况更新**: 新增"找到引晶数据文件数"统计项

### 技术改进 🔧

#### 代码结构优化
- **新增函数**: `find_first_crystal_seeding_power()` - 专门处理引晶功率提取逻辑
- **数据验证**: 增强的数据类型和有效性检查
- **异常处理**: 完善的错误处理和日志记录机制

#### 测试覆盖
- **测试脚本**: `test_crystal_seeding_feature.py` - 全面的功能测试
- **测试用例**: 包含有引晶状态和无引晶状态的测试数据
- **集成测试**: 完整的分析流程测试

### 文档更新 📚

#### 新增文档
- **功能说明**: `引晶功率提取功能说明.md` - 详细的功能使用指南
- **更新日志**: `CHANGELOG.md` - 版本更新记录

#### 配置文件示例
```json
{
    "crystal_seeding_keyword": "引晶",
    "input_directory": ".",
    "output_directory": "output_results",
    "time_format": "%Y-%m-%d %H:%M:%S",
    "preheating_duration_minutes": 10,
    "required_columns": [
        "cc_time",
        "cc_work_procedure", 
        "cc_residue_weight",
        "cc_main_heating_display",
        "cc_vice_heating_set",
        "cc_ccd_liquid_temperature"
    ],
    "target_procedure": "预调温",
    "encoding": "utf-8"
}
```

### 输出格式更新 📊

#### CSV结果文件
新增字段位置：`end_temperature_celsius` 之后，`main_total_energy_kwh` 之前

| 字段名 | 说明 | 单位 | 示例值 |
|--------|------|------|--------|
| `first_crystal_seeding_main_power_kw` | 第一次引晶时的主功率 | kW | 45.2 |

#### 特殊值说明
- **-1**: 表示未找到引晶状态或数据无效
- **正数值**: 表示成功提取的引晶主功率值

### 兼容性 🔄

- **向后兼容**: 完全兼容现有功能和数据格式
- **配置兼容**: 自动创建默认配置，无需手动修改现有配置文件
- **数据兼容**: 支持现有的CSV数据格式和编码方式

### 测试结果 ✅

#### 功能测试
- ✅ 引晶功率提取函数测试通过
- ✅ 完整分析流程测试通过
- ✅ 异常处理测试通过
- ✅ 配置文件加载测试通过

#### 测试数据验证
```
测试1：包含引晶状态的数据
预期结果: 45.2 kW
实际结果: 45.2 kW
测试结果: ✅ 通过

测试2：不包含引晶状态的数据
预期结果: None
实际结果: None  
测试结果: ✅ 通过
```

### 使用方法 🚀

#### 运行分析
```bash
python run_analysis_fixed.py
```

#### 查看结果
- **详细结果**: `output_results/[文件夹名]_analysis.csv`
- **汇总结果**: `output_results/all_folders_summary.csv`
- **统计报告**: `output_results/analysis_report.txt`

#### 自定义配置
编辑 `config.json` 文件中的 `crystal_seeding_keyword` 字段来修改引晶关键词。

### 注意事项 ⚠️

1. **数据格式**: 确保CSV文件包含必需的 `cc_work_procedure` 和 `cc_main_heating_display` 字段
2. **编码支持**: 自动检测UTF-8、GBK、GB2312编码格式
3. **关键词匹配**: 关键词匹配区分大小写，请确保配置正确
4. **日志查看**: 详细的操作日志记录在 `crystal_analysis.log` 文件中

### 开发信息 👨‍💻

- **版本**: v2.1.0
- **开发日期**: 2025-01-01
- **兼容性**: Python 3.6+
- **依赖**: pandas, numpy, datetime, json, logging

---

## [v2.0.0] - 之前版本

### 基础功能
- 拉晶设备运行数据分析
- CSV文件批量处理
- 能量计算和效率分析
- 硅理论热能需求计算
- 统计报告生成
