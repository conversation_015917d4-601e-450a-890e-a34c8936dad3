# 首投/复投功能使用指南

## 📋 功能概述

本项目新增了首投/复投分析功能，能够根据 `cc_feed_number` 字段自动判断每次控温过程的类型，并提供相应的筛选和统计功能。

### 🎯 判断逻辑

- **首投**：如果数据文件中 `cc_feed_number` 字段有任何记录**直接等于1**，则整个文件标记为"首投"
- **复投**：如果数据文件中 `cc_feed_number` 字段没有任何记录等于1，则整个文件标记为"复投"

### ✨ 主要功能

1. **自动分类**：自动识别每个数据文件的首投/复投类型
2. **数据筛选**：支持按首投/复投类型筛选分析结果
3. **统计分析**：提供详细的首投/复投统计信息
4. **对比分析**：支持首投与复投的能效对比
5. **交互界面**：提供用户友好的交互式操作界面

## 🚀 快速开始

### 方法1：使用交互式分析器（推荐）

```bash
python interactive_feed_analyzer.py
```

这是最用户友好的方式，提供图形化菜单界面，支持：
- 选择分析类型（全部/首投/复投/对比）
- 实时查看分析结果
- 详细统计报告
- 结果文件管理

### 方法2：使用筛选脚本

```bash
python run_analysis_with_feed_filter.py
```

提供命令行交互界面，支持选择筛选条件。

### 方法3：使用原始分析器

修改 `config.json` 文件中的筛选设置：

```json
{
    "feed_type_analysis": {
        "enabled": true,
        "feed_number_column": "cc_feed_number",
        "filter_type": "全部"  // 可选: "首投", "复投", "全部"
    }
}
```

然后运行：
```bash
python crystal_data_analyzer.py
```

## 📊 输出文件说明

### 主要输出文件

1. **all_folders_summary.csv**
   - 包含所有文件的分析结果
   - 新增字段：
     - `feed_type`: 首投/复投类型
     - `cc_feed_number_values`: cc_feed_number字段的唯一值
     - `feed_number_1_records`: 等于1的记录数量

2. **filtered_[类型]_summary.csv**（如果选择了筛选）
   - 筛选后的数据结果
   - 格式与主汇总文件相同

3. **analysis_report.txt/json**
   - 详细统计报告
   - 包含首投/复投统计信息

### 新增统计信息

```json
{
    "首投/复投统计": {
        "首投文件数": 15,
        "复投文件数": 25,
        "未知类型文件数": 0,
        "首投文件比例(%)": 37.5,
        "复投文件比例(%)": 62.5
    }
}
```

## 🔧 配置选项

### feed_type_analysis 配置块

```json
{
    "feed_type_analysis": {
        "enabled": true,                    // 是否启用首投/复投分析
        "feed_number_column": "cc_feed_number",  // 字段名称
        "filter_type": "全部"               // 筛选类型
    }
}
```

### filter_type 选项

- `"全部"`: 分析所有数据，不进行筛选
- `"首投"`: 只分析首投类型的数据
- `"复投"`: 只分析复投类型的数据

## 📈 使用示例

### 示例1：查看所有数据的首投/复投分布

```bash
python interactive_feed_analyzer.py
# 选择 "1. 全部数据分析"
# 选择 "1. 查看汇总统计"
```

### 示例2：只分析首投数据

```bash
python interactive_feed_analyzer.py
# 选择 "2. 仅分析首投数据"
```

### 示例3：对比首投和复投的能效

```bash
python interactive_feed_analyzer.py
# 选择 "4. 对比分析"
```

## 🧪 测试功能

运行测试脚本验证功能：

```bash
python test_feed_functionality.py
```

测试内容包括：
- 分类逻辑正确性
- 真实数据分析
- 筛选功能
- cc_feed_number字段检测

## 📋 实际数据示例

根据测试结果，以下是一些实际数据的分类情况：

| 文件夹 | cc_feed_number值 | 分类结果 | 说明 |
|--------|------------------|----------|------|
| analoga01 | [7, 8] | 复投 | 没有等于1的值 |
| analoga02 | [0, 1] | 首投 | 包含等于1的值 |
| analoga03 | [1, 2] | 首投 | 包含等于1的值 |
| analoga11 | [1, 2] | 首投 | 包含等于1的值 |
| analoga21 | [7, 8] | 复投 | 没有等于1的值 |

## ⚠️ 注意事项

1. **数据要求**：确保CSV文件中包含 `cc_feed_number` 字段
2. **编码格式**：建议使用UTF-8编码
3. **数据完整性**：如果 `cc_feed_number` 字段缺失，该文件将被标记为"未知"类型
4. **性能考虑**：大量文件分析时可能需要较长时间

## 🔍 故障排除

### 常见问题

1. **找不到cc_feed_number字段**
   - 检查CSV文件是否包含该字段
   - 确认字段名称拼写正确

2. **分析结果为"未知"**
   - 检查数据文件格式
   - 确认cc_feed_number字段有有效数据

3. **筛选结果为空**
   - 确认选择的筛选类型在数据中存在
   - 检查数据分类是否正确

### 调试方法

1. 运行测试脚本：`python test_feed_functionality.py`
2. 检查日志输出中的错误信息
3. 使用交互式分析器查看详细结果

## 📞 技术支持

如果遇到问题，请：
1. 首先运行测试脚本确认功能状态
2. 检查数据文件格式和内容
3. 查看日志文件中的详细错误信息
4. 确认配置文件设置正确

---

*本功能已通过全面测试，确保分类逻辑正确和功能稳定。*
