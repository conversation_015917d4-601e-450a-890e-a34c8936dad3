import pandas as pd
import yaml
import pickle
import numpy as np
from os.path import join
import math
#from kongwen_power_control.beta_version.v1.dynamicheating import calculate

class KongwenGonglvCorrectionModel():
    # Phase
    INIT = 0
    VICE_CLOSE1 = 1
    VICE_CLOSE2 = 2
    VICE_REOPEN = 3
    ALMOST_DONE = 4
    DONE = 5

    @classmethod
    def from_path(cls, config_path):
        m = cls()
        m.load_config(config_path)
        return m

    def find_similar_records(self, file_path, product, heat_field_size, device, yinjing_gonglv,
                             total_add_weight, total_add_time, last_jialiao_weight,
                             last_jialiao_time, last_jialiao_interval_time,
                             last_but_one_jialiao_weight, last_but_one_jialiao_time,
                             last_but_one_jialiao_interval_time):
        """
        根据指定条件在历史记录中找到最相似的记录。

        :param file_path: 历史数据的文件路径
        :param product: 产品类型
        :param heat_field_size: 热场尺寸
        :param device: 炉台号
        :param yinjing_gonglv: 引晶功率
        :param total_add_weight: 总加料量
        :param total_add_time: 总加料时长
        :param last_jialiao_weight: 最后一桶加料量
        :param last_jialiao_time: 最后一桶加料时长
        :param last_jialiao_interval_time: 最后一桶加料间隔时间
        :param last_but_one_jialiao_weight: 倒数第二桶加料量
        :param last_but_one_jialiao_time: 倒数第二桶加料时长
        :param last_but_one_jialiao_interval_time: 倒数第二桶加料间隔时间
        :return: 副液比例、液比例调整值、主加热变化值和匹配的完整记录
        """
        try:
            # 加载数据
            data = pd.read_excel(file_path)

            # 初步筛选基础条件
            filtered_data = data[
                (data['device'] == device) &
                (data['product'] == product) &
                (data['heat_field_size'] == heat_field_size)
                ]
            if filtered_data.empty:
                filtered_data = data[
                    (data['product'] == product) &
                    (data['heat_field_size'] == heat_field_size)
                    ]

            # 定义筛选条件及参数
            filters = [
                ('yinjing_gonglv', yinjing_gonglv, 0.3, 20),
                ('all_jialiao_weight', total_add_weight, 50, 15),
                ('all_jialiao_time', total_add_time, 50, 10),
                ('last_jialiao_weight', last_jialiao_weight, 50, 10),
                ('last_jialiao_time', last_jialiao_time, 50, 10),
                ('last_jialiao_interval_time', last_jialiao_interval_time, 3, 10),
                ('last_but_one_jialiao_weight', last_but_one_jialiao_weight, 50, 10),
                ('last_but_one_jialiao_time', last_but_one_jialiao_time, 50, 5),
                ('last_but_one_jialiao_interval_time', last_but_one_jialiao_interval_time, 3, 1),
            ]

            # 按顺序应用筛选条件
            for column, target, tolerance, top_n in filters:
                filtered_data['diff'] = (filtered_data[column] - target).abs()
                filtered_subset = filtered_data[
                    (filtered_data[column] >= target - tolerance) &
                    (filtered_data[column] <= target + tolerance)
                    ]
                if filtered_subset.empty:
                    filtered_data = filtered_data.nsmallest(top_n, 'diff')
                else:
                    filtered_data = filtered_subset

                if filtered_data.empty:
                    return 0, 0, 0, None

            # 获取最相似记录
            result = filtered_data.iloc[0:1]

            # 计算副液比例
            if 1440 <= result['dingguo_10min_yewen'].iloc[0] <= 1458:
                temperature_diff = 1448 - result['dingguo_10min_yewen'].iloc[0]
                Q = abs(700 * total_add_weight * temperature_diff) / 1000
                t = np.sqrt(2 * Q / result['xishu1_pt_ratio'].iloc[0])

                full_interval_time = result['full_interval_time'].iloc[0]
                full_liquid_proportion = result['full_liquid_proportion'].iloc[0]
                vice_liquid_proportion = result['vice_liquid_proportion'].iloc[0]

                if temperature_diff < 0:
                    if t < full_interval_time / 2:
                        proportion = (full_interval_time - t) / full_interval_time * full_liquid_proportion
                    else:
                        proportion = t / full_interval_time * full_liquid_proportion
                else:
                    if t < full_interval_time / 2:
                        proportion = full_liquid_proportion + (t / full_interval_time * full_liquid_proportion)
                    else:
                        proportion = full_liquid_proportion + (
                                (full_interval_time - t) / full_interval_time * full_liquid_proportion)

                proportion = np.clip(proportion, 40, 60)
                vice_liquid_proportion = np.clip(vice_liquid_proportion, 30, 40)

                if vice_liquid_proportion >= proportion:
                    vice_liquid_proportion = proportion
                return float(vice_liquid_proportion), float(proportion), float(
                    result['change_main_heating'].iloc[0]), result

            return 0, 0, 0, None
        except Exception as e:
            print(f"Error: {e}")
            return 0, 0, 0, None

    def load_config(self, config_path):
        with open(config_path,encoding='utf-8') as f:
            config = yaml.load(f, Loader=yaml.FullLoader)['gonglv']
        self.config = config
        self.origin_vice_close_ratio = config['vice_close_ratio']  # [half, all]
        self.high_ratio = config['high_ratio']
        self.time_range = config['time_range']
        self.origin_done_power = config['done_power']
        self.done_power_k = config['done_power_k']
        self.origin_init_power = config['init_power']
        self.begin_t = config['begin_t']
        self.undo_main_ratio = config['undo_main_ratio']
        self.done_ratio = config['done_ratio']
        self.down_time = config['down_time']
        self.vice_check_time = config['vice_check_time']
        self.max_reopen_time = config['max_reopen_time']
        self.key_ccd = [0] + config['key_ccd'] + [10000]
        self.origin_key_power = config['key_power']
        self.key_power_k = config['key_power_k']
        self.vice_time_range = config['vice_time_range']
        self.melting_ccd3 = config['melting_ccd3']
        self.melting_power_k = config['melting_power_k']
        self.dynamic_vice_heating = config['dynamic_vice_heating']
        self.kongwen_target = 1448
        self.ladder_vice_power = 0
        self.turnover_threshold, self.film_threshold = 40,40 #翻料占比阈值，薄膜料占比阈值
        self.adjust_space = 10  #薄膜料调整空间
        self.turnover_delay = 5 #翻料延时
        self.one_vice_close_ratio = [25,40] #加一桶料的溶液比阈值
        self.main_delay = 5  # 主加调整延时


    def load_model(self, keycurves_path):
        with open(keycurves_path, 'rb') as f:
            self.keycurves = pickle.load(f)
        return len(self.keycurves) != 0

    def generate_thresholds_and_powers(self,start, end):
        # 确保从 start 到 end 总共选取 4 个溶液比值
        power_ratios = [0.8, 0.6, 0.3, 0.0]  # 固定的功率比例
        thresholds = [start + i * (end - start) / (len(power_ratios)-1) for i in range(len(power_ratios))]  # 等间隔选取4个值

        # 生成溶液比和功率比例的配对，使用列表而不是元组
        threshold_power_pairs = [[thresholds[i], power_ratios[i]] for i in range(len(power_ratios))]

        return threshold_power_pairs


    def setup(self, device_id, jialiao, times, power_yinjing, init_power, config, field_size, product_type,target_ccd):

        update_keys = ['high_ratio', 'time_range', 'done_power_k', 'begin_t', 'undo_main_ratio', 'done_ratio',
                       'down_time', 'vice_check_time', 'max_reopen_time', 'key_power_k', 'vice_time_range',
                       'melting_ccd3', 'melting_power_k','dynamic_vice_heating','kongwen_target','ladder_vice_power',
                       'turnover_threshold','film_threshold','one_vice_close_ratio','adjust_space','turnover_delay','main_delay']
        for key in update_keys:
            if key in config and config[key] is not None:
                setattr(self, key, config[key])

        if float(product_type) in [11, 12] and float(field_size) == 36:
            if 'time_range' not in config:
                self.time_range = [self.config['time_range'][0] - 2, self.config['time_range'][1] + 5]
                print(self.time_range)
            if 'done_power_k' not in config:
                self.done_power_k = [
                    self.config['done_power_k'][0] + 0.25,
                    self.config['done_power_k'][1] + 0.02,
                    self.config['done_power_k'][2],
                    self.config['done_power_k'][3]
                ]
        self.vice_close_ratio = None
        if 'vice_ratios' in config:
            for w1, w2, r1, r2 in config['vice_ratios']:
                if w1 <= jialiao <= w2:
                    self.vice_close_ratio = (r1, r2)
                    break
        if self.vice_close_ratio is None:
            if jialiao < 200:
                self.vice_close_ratio = [self.origin_vice_close_ratio[0] - 20] + [self.origin_vice_close_ratio[1] - 10]
            else:
                self.vice_close_ratio = self.origin_vice_close_ratio
        self.init_main_power, self.init_vice_power = init_power if len(list(filter(lambda p: p < 10, init_power))) == 0 else self.origin_init_power
        if power_yinjing < 10:
            self.mid_power, self.min_main_power, self.end_power, self.down_clip = self.origin_done_power
            self.key_power = self.origin_key_power
            self.vice_reopen_power = self.init_vice_power * 0.8
        else:
            k_func = lambda k: np.clip(power_yinjing * k, 30, 100)
            self.mid_power, self.min_main_power, self.end_power, self.down_clip = list(map(k_func, self.done_power_k))
            self.key_power = list(map(k_func, self.key_power_k))
            self.vice_reopen_power = power_yinjing * 0.5

        self.phase = __class__.INIT
        self.vice_half_closed_time, self.vice_closed_time, self.vice_reopen_time, self.vice_reopen_ratio, self.high_time, self.done_time = None, None, None, None, None, None
        self.cur_main_power, self.cur_vice_power = self.init_main_power, self.init_vice_power
        self.reopened, self.location_fixed = False, False
        self.before_ratios = []
        self.reopened_ratios = []
        self.before_ratios_done = []
        self.before_reopened_ratios = []
        self.Quanrong = None
        self.power_change = None
        self.power_yinjing = power_yinjing
        self.product_type, self.field_size, self.jialiao = product_type, field_size, jialiao
        self.ccd3_power_adjust,self.dynamic_main_heating,self.device_id,self.jialiao = False,0,device_id,jialiao
        self.film_ratio, self.film_correction = [70, 80], None
        self.fime_data = []
        self.dynamic_setting = False
        self.dynamic_result = None
        self.target_ccd = target_ccd
        self.risk, self.history_close_vice_time, self.history_vice_ratio, self.history_fullmelting_time = None, None, None, None
        self.history_time_range, self.limit_range = None,None
        self.over_temp_ratio, self.under_temp_ratio = None, None
        self.vice_lower, self.vice_upper = None,None
        self.power_zeroed = False
        self.realtime_data = []
        self.turnover_data = []
        self.turnover_data_filter = []
        self.fime_data_filter = []
        self.ratio_data = []
        self.ratio_thresholds = self.generate_thresholds_and_powers(self.vice_close_ratio[0]-5, self.vice_close_ratio[1]+5)  #这里需要配置溶液比阈值

        if device_id =='A37' or device_id =='A39':
            self.mid_power = init_power[0]
        if float(field_size) == 36:
            self.ekf = RealTimeEKF(process_variance=9e-4, measurement_variance=0.05, max_increase_rate=0.3, rate_threshold=3, upper_limit=100)
        else:
            self.ekf = RealTimeEKF(process_variance=1e-3, measurement_variance=0.05, max_increase_rate=0.2, rate_threshold=3, upper_limit=100)

    def finish(self, end_code):
        return 0, 0, 0, 0, 0, [], [], 0, 0, 0

    def calculate_recent_extremes_mean(self,data):
        try:
            if len(data) < 2:
                return None  # 不足两个数据点无法计算极值
            recent_max = None
            recent_min = None
            # 从最后一个元素向前查找
            for value in reversed(data):
                if recent_max is None or value > recent_max:
                    recent_max = value
                if recent_min is None or value < recent_min:
                    recent_min = value
                # 如果已经找到了极大值和极小值，可以停止
                if recent_max is not None and recent_min is not None:
                    break
            # 计算并返回均值
            if recent_max is not None and recent_min is not None:
                mean_value = (recent_max + recent_min) / 2
                return mean_value
            return None
        except Exception as e:
            return None
    def is_empty(self,time_range):
        return any(x is None or (isinstance(x, float) and math.isnan(x)) for x in time_range)

    def set_vice_closed_values(self, t):
        """封装 self.phase, self.cur_vice_power, self.vice_closed_time 的赋值"""
        self.phase, self.cur_vice_power, self.vice_closed_time = __class__.VICE_CLOSE2, 0, t


    def control_vice_power(self, data, ratio_thresholds, initial_power, t,vice_lower, vice_upper):
        """
        控制副功率，根据滑动窗口内的溶液比来调整副功率。

        参数：
        - data: 存储历史溶液比和时间的数组（deque 类型）
        - ratio_thresholds: 溶液比对应副功率比例的二维数组，例如 [[20, 0.8], [25, 0.5], [30, 0.3], [35, 0.0]]
        - initial_power: 初始功率
        - t: 当前时间（秒）
        - ratio: 当前溶液比
        返回：
        - 返回调整后的副功率
        """
        # 记录当前的溶液比和时间
        if self.power_zeroed:
            return 0

        # 获取最近5分钟的数据（即从 t 到 t - 300s 之间的数据）
        recent_ratios = [r for time, r in data if t - time <= 300]

        # 如果最近5分钟的溶液比数据不足，则返回 None
        if len(recent_ratios) == 0:
            return initial_power  # 没有足够数据来计算副功率

        # 获取滑动窗口内的最大溶液比
        max_ratio = min(recent_ratios)

        # 如果最大溶液比小于20，则副功率为初始功率
        if max_ratio < ratio_thresholds[0][0]:
            print(f"溶液比小于20，副功率保持为初始功率 {initial_power}")
            self.last_power = initial_power
            return initial_power

        # 根据溶液比的范围调整副功率
        for i in range(1, len(ratio_thresholds)):
            lower_threshold = ratio_thresholds[i - 1][0]  # 上一个阈值
            upper_threshold = ratio_thresholds[i][0]  # 当前阈值
            power_ratio = ratio_thresholds[i - 1][1]  # 上一个阈值对应的副功率比例

            # 如果当前溶液比在两个阈值之间，且大于等于下限小于上限
            if lower_threshold <= max_ratio < upper_threshold:
                print(f"溶液比大于等于 {lower_threshold} 且小于 {upper_threshold}，调整副功率为 {initial_power * power_ratio}")
                self.last_power = initial_power * power_ratio
                return initial_power * power_ratio  # 返回对应副功率

        # 如果最大溶液比大于最后一个阈值
        if max_ratio >= ratio_thresholds[-1][0]:
            power_ratio = ratio_thresholds[-1][1]
            print(f"溶液比大于等于 {ratio_thresholds[-1][0]}，调整副功率为 {initial_power * power_ratio}")
            self.last_power = initial_power * power_ratio
            self.power_zeroed = True
            return initial_power * power_ratio  # 返回对应副功率

        # 如果没有满足条件，返回上一个副功率值
        if self.last_power is not None:
            print(f"没有满足条件，返回上一个副功率 {self.last_power}")
            return self.last_power  # 返回上一个副功率
        else:
            print(f"没有满足条件，副功率设置为初始功率 {initial_power}")
            self.last_power = initial_power
            return initial_power  # 如果没有上一个副功率，返回初始功率


    #此函数用于计算滑窗内翻料占比的最大值
    def max_turnover_ratio(self, turnover_data, window_size=120):
        """
        计算 turnover_data 数组中，过去 window_size 秒内的最大翻料占比 (turnover_ratio) 值。

        参数：
        turnover_data (list of tuples): 包含时间和翻料占比的元组 [(time, turnover_ratio), ...]
        window_size (int): 时间窗口大小，单位为秒，默认 120秒

        返回：
        float: 滤波后的最大值，如果没有符合条件的数据，返回 None
        """
        if not turnover_data:
            return None  # 如果 turnover_data 为空，直接返回 None

        latest_time = turnover_data[-1][0]
        max_value = float('-inf')

        for time, value in reversed(turnover_data):
            if latest_time - time > window_size:  # 超过时间窗口
                break
            max_value = max(max_value, value)

        # 如果没有符合条件的数据，返回 None 或者其他你认为合适的值
        if max_value == float('-inf'):
            return max(turnover_data, key=lambda x: x[1])

        return max_value

    def min_turnover_ratio(self,ratio_data,window_size=120):
        """
        计算过去120秒内的最小 ratio 值。
        参数：
        ratio_data (list of tuples): 包含时间和比值的元组 [(t, ratio), ...]
        返回：
        float: 过去120秒内 ratio 的最小值
        """
        # 获取当前时间（即最后一个时间点）
        latest_time = ratio_data[-1][0]

        # 筛选出过去120秒内的数据
        last_120_seconds_data = [
            (t, ratio) for t, ratio in reversed(ratio_data)
            if latest_time - t <= window_size
        ]
        # 确保至少有一个数据点
        if not last_120_seconds_data:
            return 0

        # 提取出所有在过去120秒内的数据的 ratio
        ratios = [ratio for t, ratio in last_120_seconds_data]

        # 返回过去120秒内 ratio 的最小值
        min_ratio = min(ratios)

        return min_ratio

    def calculate_slope(self,fime_data):
        """
        计算过去两分钟（120秒）内薄膜占比的拟合直线斜率。

        参数：
        fime_data (list of tuples): 包含时间和薄膜占比的元组 [(t, film_ratio), ...]

        返回：
        float: 过去两分钟内薄膜占比的拟合直线斜率
        """
        # 获取当前时间（即最后一个时间点）
        latest_time = fime_data[-1][0]

        # 选择过去两分钟（120秒）内的数据
        last_two_minutes_data = [
            (t, film_ratio) for t, film_ratio in reversed(fime_data)
            if latest_time - t <= 120
        ]

        # 确保有足够的数据来进行拟合（至少两个数据点）
        if len(last_two_minutes_data) < 2:
            return 0

        # 提取时间和薄膜占比的列表
        times = np.array([t for t, film_ratio in last_two_minutes_data])
        film_ratios = np.array([film_ratio for t, film_ratio in last_two_minutes_data])

        # 使用 np.polyfit 进行线性拟合（一次多项式）
        coefficients = np.polyfit(times, film_ratios, 1)  # 拟合一次多项式（直线）

        # 获取拟合直线的斜率
        slope = coefficients[0]

        return slope

    def predict(self, t, ratio, ccd, ccd3, fullmelting,sum_jialiao_time,last_jialiao_time,last_jialiao_weight,last_Interval_time,barrelage,
                last_but_one_jialiao_weight,last_but_one_jialiao_time,last_but_one_jialiao_interval_time,film_ratio,turnover_ratio):

        self.realtime_data.append((t,ratio))
        self.fime_data.append((t,film_ratio))
        self.turnover_data.append((t,turnover_ratio))
        self.turnover_data_filter.append((t,self.max_turnover_ratio(self.turnover_data,window_size=60)))
        self.fime_data_filter.append((t,self.max_turnover_ratio(self.fime_data,window_size=60)))
        self.ratio_data.append((t,ratio))
        ratio_delay = self.min_turnover_ratio(self.ratio_data, window_size=120)

        print(ratio_delay,ratio)

        t = t / 60  # second -> min

        #处理只加一桶料的情况
        if int(barrelage) == 1:
            self.vice_close_ratio = self.one_vice_close_ratio



        EKF_ratio = self.ekf.update(ratio)

        if t <= self.begin_t:
            return self.cur_main_power, self.cur_vice_power

        values = [last_Interval_time / 60, last_jialiao_weight, last_jialiao_time / 60,
                  last_but_one_jialiao_interval_time / 60, last_but_one_jialiao_weight,
                  last_but_one_jialiao_time / 60]



        if self.ladder_vice_power == 1:
            self.cur_vice_power = self.control_vice_power(self.realtime_data, self.ratio_thresholds, self.init_vice_power, t*60,self.vice_lower, self.vice_upper)
            if self.cur_vice_power == 0: self.phase, self.vice_closed_time = __class__.VICE_CLOSE2, t
        if self.phase == __class__.INIT:
            #翻料
            if self.min_turnover_ratio(self.turnover_data_filter,window_size=self.turnover_delay*60) < self.turnover_threshold:
                # 无薄膜料
                if self.fime_data_filter[-1][1] < self.film_threshold or ratio_delay > 50:
                    if ratio_delay >= self.vice_close_ratio[0]:
                        self.phase, self.cur_vice_power, self.vice_half_closed_time = __class__.VICE_CLOSE1, self.init_vice_power / 2, t
                # 有薄膜料
                elif self.fime_data_filter[-1][1] >= self.film_threshold:
                    if ratio_delay >= self.vice_close_ratio[0]-self.adjust_space:
                        self.phase, self.cur_vice_power, self.vice_half_closed_time = __class__.VICE_CLOSE1, self.init_vice_power / 2, t


        elif self.phase == __class__.VICE_CLOSE1:
            # 提取共用的判断条件
            close_condition = (ratio_delay >= self.vice_close_ratio[1]) or ratio_delay >= 50
            if self.min_turnover_ratio(self.turnover_data_filter, window_size = self.turnover_delay*60) < self.turnover_threshold or ratio_delay > 50:
                # 无薄膜料
                if self.fime_data_filter[-1][1] < self.film_threshold:
                    if close_condition: self.set_vice_closed_values(t)
                # 有薄膜料
                if self.fime_data_filter[-1][1] >= self.film_threshold:
                    if (ratio_delay >= (self.vice_close_ratio[1]-self.adjust_space)) or ratio_delay >= 50:
                        self.set_vice_closed_values(t)



        elif self.phase in [__class__.VICE_CLOSE2, __class__.VICE_REOPEN]:
            if self.phase == __class__.VICE_REOPEN:
                self.reopened_ratios.append((t, ratio))
                if (t - self.reopened_ratios[0][0] >= 3 and np.mean([r_ >= min(70, self.vice_reopen_ratio + 10) for t_, r_ in self.reopened_ratios if t - t_ <= 3]) >= 0.5) or t - self.vice_reopen_time >= self.max_reopen_time:
                    if t - self.vice_reopen_time > 10:
                        self.phase, self.cur_vice_power,self.vice_closed_time = __class__.VICE_CLOSE2, 0,t

            #主功率增加延时判断
            if self.min_turnover_ratio(self.ratio_data,window_size=self.main_delay*60) >= self.high_ratio and int(barrelage)>1:
                self.high_time = t

                # '''时间范围参数更新'''
                # if self.history_time_range:
                #     if not self.is_empty(self.history_time_range):
                #         self.time_range = self.history_time_range
                dt, dp = self.time_range[1] - self.time_range[0], self.mid_power - self.min_main_power
                p = (t - self.vice_closed_time - self.time_range[0]) / dt * dp + self.min_main_power
                self.cur_main_power, self.cur_vice_power = np.clip(p, self.down_clip, self.mid_power), 0

                if self.jialiao > 150 and self.ladder_vice_power == 0:
                    if self.vice_closed_time - self.vice_half_closed_time > 10:
                        during_t = self.vice_closed_time - self.vice_half_closed_time
                        self.cur_main_power += (during_t / (self.vice_time_range[1] - self.vice_time_range[0])) * self.down_clip
                        self.cur_main_power = np.clip(self.cur_main_power, self.down_clip, self.mid_power)
                        self.power_change = np.clip(self.cur_main_power, self.down_clip, self.mid_power)
                    else:self.power_change = self.cur_main_power
                else:
                    self.power_change = self.cur_main_power
                if (t - self.vice_closed_time) >= self.time_range[1]:
                    self.cur_main_power = self.init_main_power
                    self.power_change = self.cur_main_power
                elif (t - self.vice_closed_time) <= self.time_range[0]:
                    self.cur_main_power = self.down_clip
                    self.power_change = self.cur_main_power
                # '''历史上下限限定'''
                # if self.limit_range:
                #     if self.limit_range[0] and (t - self.vice_closed_time) <= self.limit_range[0]:
                #         self.cur_main_power = self.down_clip
                #     if self.limit_range[1] and (t - self.vice_closed_time) >= self.limit_range[1]:
                #         self.cur_main_power = self.init_main_power
                self.phase = __class__.ALMOST_DONE
            elif (not self.reopened) and t - self.vice_closed_time >= self.vice_check_time  and self.ladder_vice_power==0:
                self.before_reopened_ratios.append((t, ratio))
                if ratio - self.vice_close_ratio[1] <= 10 and int(barrelage)>1:
                    if (t - self.before_reopened_ratios[0][0] >= 4 and np.mean([r_ <= (self.vice_close_ratio[1] + 5) for t_, r_ in self.before_reopened_ratios if t - t_ <= 4]) >= 0.4):
                        self.phase, self.cur_vice_power, self.vice_reopen_time, self.vice_reopen_ratio, self.reopened = __class__.VICE_REOPEN, self.vice_reopen_power * 1.2, t, ratio, True
                    if (t - self.before_reopened_ratios[0][0] >= 4 and np.mean([r_ <= (self.vice_close_ratio[1] + 10) for t_, r_ in self.before_reopened_ratios if t - t_ <= 4]) >= 0.4):
                        self.phase, self.cur_vice_power, self.vice_reopen_time, self.vice_reopen_ratio, self.reopened = __class__.VICE_REOPEN, self.vice_reopen_power, t, ratio, True
        elif self.phase == __class__.ALMOST_DONE:
            self.before_ratios.append((t, ratio))
            if ratio >= self.done_ratio:
                if self.done_time is None:
                    self.done_time = t
                    self.Quanrong = True
                elif t - self.done_time >= 5: # 5min
                    self.phase = __class__.DONE

            else:
                self.done_time = None
                if self.high_time is not None:
                    if ratio <= self.undo_main_ratio and int(barrelage)>1:
                        self.cur_main_power = np.clip(self.power_yinjing * 1.25, self.power_change, self.init_main_power)
                    if self.Quanrong is True and self.done_time is None and int(barrelage)>1:
                        self.before_ratios_done.append((t, ratio))
                        if (t - self.before_ratios_done[0][0] >= 4 and np.mean([r_ <= 94 for t_, r_ in self.before_ratios_done if t - t_ <= 5]) >= 0.5) or (t - self.high_time) >= self.down_time:
                            self.high_time, self.cur_main_power = None, np.clip(self.mid_power, self.power_change, self.init_main_power)
        elif self.phase == __class__.DONE:
            pass
            # if fullmelting == 1 and ccd3 is not None and self.jialiao > 200 and not self.ccd3_power_adjust:
            #     self.ccd3_power_adjust = True
            #     if ccd3 < self.melting_ccd3[0]:
            #         self.cur_main_power = np.clip(self.melting_power_k[0] * self.power_yinjing, 30,self.init_main_power)
            #     if ccd3 > self.melting_ccd3[1]:
            #         self.cur_main_power = np.clip(self.melting_power_k[1] * self.power_yinjing, 30,self.init_main_power)
            #     # if ccd3 >= self.melting_ccd3[0] and ccd3 <= self.melting_ccd3[1]:
            #     #     k = (self.melting_power_k[0] - self.melting_power_k[1]) * self.power_yinjing
            #     #     self.cur_main_power = np.clip(self.cur_main_power + (k * (ccd3 - self.melting_ccd3[0])), 30,self.init_main_power)

            if self.high_time is not None and (t - self.high_time) >= self.down_time:
                self.high_time = None
            if ccd > 0 and not self.location_fixed:
                self.high_time = None
                if ccd < self.target_ccd:
                    self.cur_main_power = np.clip(self.mid_power, 0, self.mid_power)
                else:
                    self.cur_main_power = np.clip(self.down_clip, 0, self.down_clip)

        return self.cur_main_power, self.cur_vice_power


class RealTimeEKF:
    def __init__(self, process_variance=1e-5, measurement_variance=0.1, max_increase_rate=0.1, rate_threshold=0.5,upper_limit=None):
        """
        初始化扩展卡尔曼滤波器
        :param process_variance: 过程噪声的方差
        :param measurement_variance: 测量噪声的方差
        :param upper_limit: 滤波结果的上限（可选）
        """
        self.process_variance = process_variance
        self.measurement_variance = measurement_variance

        #原始噪声值保存
        self.original_process_variance = process_variance
        self.original_measurement_variance = measurement_variance


        self.max_increase_rate = max_increase_rate
        self.rate_threshold = rate_threshold
        self.upper_limit = upper_limit

        self.x_estimate = None  # 当前估计值
        self.p_estimate = None  # 当前估计误差协方差
        self.prev_measurement = None

    def update(self, measurement):
        """
        使用新的测量值更新滤波器状态
        :param measurement: 实时传入的测量值
        :return: 滤波后的估计值
        """
        if self.x_estimate is None:
            # 初始化
            self.x_estimate = measurement
            self.p_estimate = 1.0
        else:
            # # 假设观测方程为对数函数：h(x) = log(1 + x)
            # h = np.log1p(self.x_estimate)
            # h_derivative = 1 / (1 + self.x_estimate)

            # 预测阶段
            x_predict = self.x_estimate
            p_predict = self.p_estimate + self.process_variance

            # 更新阶段
            kalman_gain = p_predict / (p_predict + self.measurement_variance)
            self.x_estimate = x_predict + kalman_gain * (measurement - x_predict)
            self.p_estimate = (1 - kalman_gain) * p_predict

            # 速率检测，判断溶液比是否突增
            if self.prev_measurement is not None:
                rate = measurement - self.prev_measurement  # 用原始测量数据的变化来计算速率
                if rate > self.rate_threshold:  # 溶液比增大超过阈值，判定为突增
                    #print('到这里了吗')
                    # 增大测量噪声，减小滤波器对突增的敏感度
                    self.measurement_variance = min(self.original_measurement_variance + (rate - self.rate_threshold) * 0.2,
                                                    self.max_increase_rate)
                    self.process_variance = 0.9 * self.original_process_variance
                    #print('更新后的',self.measurement_variance,self.process_variance)
                else:
                    # 恢复正常噪声值
                    self.measurement_variance = self.original_measurement_variance
                    self.process_variance = self.original_process_variance

            # 如果设置了上限，将滤波结果限制在上限以内
            if self.upper_limit is not None:
                self.x_estimate = min(self.x_estimate, self.upper_limit)

        # 更新上一个测量值和估计值
        self.prev_measurement = measurement

        return self.x_estimate