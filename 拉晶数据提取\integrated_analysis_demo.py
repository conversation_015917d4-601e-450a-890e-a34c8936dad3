#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合后的首投/复投分析功能演示
Integrated Feed Type Analysis Demo

展示完全整合到现有代码中的首投/复投功能：
1. 无缝集成到现有分析流程
2. 智能筛选和自动分类
3. 详细统计和对比分析
4. 多种输出格式

作者：AI Assistant
日期：2025-01-07
"""

import os
import json
from crystal_data_analyzer import CrystalDataAnalyzer


def demo_integrated_functionality():
    """演示完全整合的功能"""
    print("🎯 整合后的首投/复投分析功能演示")
    print("=" * 60)
    print("展示功能已完全整合到现有 crystal_data_analyzer.py 中")
    
    # 创建演示配置
    demo_config = {
        "input_directory": ".",
        "output_directory": "integrated_demo_results",
        "time_format": "%Y-%m-%d %H:%M:%S",
        "preheating_duration_minutes": 10,
        "required_columns": [
            "cc_time",
            "cc_work_procedure",
            "cc_residue_weight",
            "cc_main_heating_display",
            "cc_vice_heating_set",
            "cc_ccd_liquid_temperature"
        ],
        "target_procedure": "预调温",
        "crystal_seeding_keyword": "引晶",
        "encoding": "utf-8",
        "feed_type_analysis": {
            "enabled": True,
            "feed_number_column": "cc_feed_number",
            "filter_type": "全部"  # 可以设置为 "首投", "复投", "全部"
        }
    }
    
    config_file = "integrated_demo_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(demo_config, f, ensure_ascii=False, indent=4)
    
    print(f"\n📋 配置文件已创建: {config_file}")
    print("🔧 配置特点:")
    print("  • 启用首投/复投分析")
    print("  • 自动生成所有类型的筛选文件")
    print("  • 详细统计报告包含对比分析")
    
    try:
        # 创建分析器
        analyzer = CrystalDataAnalyzer(config_file)
        
        print(f"\n🚀 开始分析...")
        print("📁 分析范围: 前5个设备文件夹")
        
        # 模拟分析前5个文件夹
        test_folders = ['analoga01', 'analoga02', 'analoga03', 'analoga04', 'analoga05']
        all_results = []
        
        print(f"\n📊 分析进度:")
        print("-" * 50)
        
        for i, folder in enumerate(test_folders, 1):
            csv_file = f"{folder}/extracted_content_1.csv"
            if os.path.exists(csv_file):
                result = analyzer.analyze_single_csv(csv_file)
                if result:
                    all_results.append(result)
                    feed_type = result['feed_type']
                    efficiency = result['energy_efficiency_percent']
                    feed_values = result['cc_feed_number_values']
                    
                    emoji = "🥇" if feed_type == "首投" else "🔄"
                    print(f"{i}. {folder}: {emoji} {feed_type} | 能效:{efficiency:.1f}% | cc_feed_number:{feed_values}")
        
        if not all_results:
            print("❌ 没有找到可分析的数据文件")
            return
        
        print(f"\n✅ 分析完成，共处理 {len(all_results)} 个文件")
        
        # 展示筛选功能
        print(f"\n🔍 筛选功能演示:")
        print("-" * 30)
        
        first_results = analyzer.filter_results_by_feed_type(all_results, "首投")
        repeat_results = analyzer.filter_results_by_feed_type(all_results, "复投")
        
        print(f"🥇 首投文件: {len(first_results)} 个")
        for r in first_results:
            print(f"   • {r['folder_name']}: 能效 {r['energy_efficiency_percent']:.1f}%")
        
        print(f"🔄 复投文件: {len(repeat_results)} 个")
        for r in repeat_results:
            print(f"   • {r['folder_name']}: 能效 {r['energy_efficiency_percent']:.1f}%")
        
        # 展示详细统计
        print(f"\n📈 详细统计分析:")
        print("-" * 30)
        
        feed_summary = analyzer.get_feed_type_summary(all_results)
        
        if feed_summary:
            basic_stats = feed_summary.get("基础统计", {})
            efficiency_stats = feed_summary.get("能效对比", {})
            
            print(f"📊 基础统计:")
            print(f"   • 首投比例: {basic_stats.get('首投比例(%)', 0):.1f}%")
            print(f"   • 复投比例: {basic_stats.get('复投比例(%)', 0):.1f}%")
            
            print(f"⚡ 能效对比:")
            first_avg = efficiency_stats.get('首投平均能效(%)', 0)
            repeat_avg = efficiency_stats.get('复投平均能效(%)', 0)
            print(f"   • 首投平均能效: {first_avg:.2f}%")
            print(f"   • 复投平均能效: {repeat_avg:.2f}%")
            
            if first_avg > repeat_avg:
                print(f"   🏆 首投能效更高，高出 {first_avg - repeat_avg:.2f}%")
            elif repeat_avg > first_avg:
                print(f"   🏆 复投能效更高，高出 {repeat_avg - first_avg:.2f}%")
            else:
                print(f"   ⚖️ 首投和复投能效相当")
        
        # 模拟保存结果文件
        output_dir = demo_config['output_directory']
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存主要结果文件
        analyzer.save_results_to_csv(all_results, os.path.join(output_dir, "all_folders_summary.csv"))
        
        # 保存筛选结果文件
        if first_results:
            analyzer.save_results_to_csv(first_results, os.path.join(output_dir, "auto_filtered_首投_summary.csv"))
        if repeat_results:
            analyzer.save_results_to_csv(repeat_results, os.path.join(output_dir, "auto_filtered_复投_summary.csv"))
        
        # 生成统计报告
        analyzer.generate_summary_report(all_results, output_dir, feed_summary)
        
        print(f"\n📁 输出文件:")
        print("-" * 30)
        print(f"📄 完整结果: {output_dir}/all_folders_summary.csv")
        if first_results:
            print(f"🥇 首投数据: {output_dir}/auto_filtered_首投_summary.csv")
        if repeat_results:
            print(f"🔄 复投数据: {output_dir}/auto_filtered_复投_summary.csv")
        print(f"📊 统计报告: {output_dir}/analysis_report.json")
        print(f"📝 文本报告: {output_dir}/analysis_report.txt")
        
        print(f"\n🎉 整合演示完成！")
        print("=" * 60)
        print("✅ 首投/复投功能已完全整合到现有代码中")
        print("✅ 保持了原有代码的架构和风格")
        print("✅ 增强了筛选和统计功能")
        print("✅ 提供了多种输出格式")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
    finally:
        # 清理临时文件
        try:
            os.remove(config_file)
        except:
            pass


def show_integration_summary():
    """显示整合总结"""
    print("\n📋 整合功能总结")
    print("=" * 50)
    
    print("\n🔧 核心整合点:")
    print("1. analyze_single_csv() - 添加首投/复投分析")
    print("2. run_analysis() - 集成筛选和自动分类")
    print("3. generate_summary_report() - 增强统计报告")
    print("4. 新增 filter_results_by_feed_type() 方法")
    print("5. 新增 get_feed_type_summary() 方法")
    
    print("\n📊 输出文件增强:")
    print("• all_folders_summary.csv - 包含首投/复投字段")
    print("• auto_filtered_首投_summary.csv - 自动生成首投数据")
    print("• auto_filtered_复投_summary.csv - 自动生成复投数据")
    print("• filtered_[类型]_summary.csv - 按配置筛选")
    print("• analysis_report.json/txt - 详细对比统计")
    
    print("\n⚙️ 配置选项:")
    print("• feed_type_analysis.enabled - 启用/禁用功能")
    print("• feed_type_analysis.filter_type - 筛选类型")
    print("• feed_type_analysis.feed_number_column - 字段名")
    
    print("\n🚀 使用方法:")
    print("1. 直接运行: python crystal_data_analyzer.py")
    print("2. 修改配置: 编辑 config.json")
    print("3. 交互式: python interactive_feed_analyzer.py")
    print("4. 测试功能: python test_feed_functionality.py")


def main():
    """主函数"""
    print("🎯 首投/复投功能完整整合演示")
    print("=" * 60)
    print("本演示展示功能如何完全整合到现有代码架构中")
    
    try:
        # 演示整合功能
        demo_integrated_functionality()
        
        # 显示整合总结
        show_integration_summary()
        
        print(f"\n🎊 整合完成！")
        print("现在您可以直接使用现有的 crystal_data_analyzer.py")
        print("所有首投/复投功能都已无缝集成其中！")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示出错: {e}")


if __name__ == "__main__":
    main()
