# 拉晶数据分析系统 - 引晶功率提取功能说明

## 功能概述

本次更新在拉晶数据分析系统中新增了引晶时主功率提取功能，能够自动识别并提取第一次出现"引晶"状态时的主功率数值。

## 新增功能详情

### 1. 筛选条件
- **字段**: `cc_work_procedure`
- **关键词**: "引晶"（可在配置文件中自定义）
- **匹配方式**: 字符串包含匹配，支持模糊匹配

### 2. 时间定位
- 自动查找第一次出现包含"引晶"关键词的记录
- 按时间顺序排序，确保获取最早的引晶时间点

### 3. 功率提取
- **提取字段**: `cc_main_heating_display`
- **数据类型**: 数值型（kW）
- **精度**: 保留原始精度，转换为浮点数

### 4. 结果输出
- **新增字段**: `first_crystal_seeding_main_power_kw`
- **单位**: kW（千瓦）
- **位置**: 在CSV结果文件中的第10列（end_temperature_celsius之后）

### 5. 异常处理
- **未找到引晶状态**: 字段值设为 `-1`
- **无效功率数值**: 字段值设为 `-1`
- **数据类型错误**: 字段值设为 `-1`
- **其他异常**: 记录错误日志，字段值设为 `-1`

### 6. 数据验证
- 检查功率数值是否为有效的数值型数据
- 排除空值（NaN）和非数值类型
- 确保数据类型转换的安全性

## 配置文件更新

### config.json 新增配置项

```json
{
    "crystal_seeding_keyword": "引晶"
}
```

**说明**:
- `crystal_seeding_keyword`: 引晶关键词，默认为"引晶"
- 可根据实际数据中的工艺流程名称进行调整
- 支持中文关键词

## 输出结果说明

### CSV文件字段说明

| 字段名 | 说明 | 单位 | 示例值 |
|--------|------|------|--------|
| `first_crystal_seeding_main_power_kw` | 第一次引晶时的主功率 | kW | 45.2 |

### 特殊值说明

- **-1**: 表示未找到引晶状态或数据无效
- **正数值**: 表示成功提取的引晶主功率值

## 统计报告更新

### 新增统计项

在分析报告中新增"引晶功率统计"部分：

```json
"引晶功率统计": {
    "平均引晶主功率(kW)": 42.5,
    "最高引晶主功率(kW)": 55.0,
    "最低引晶主功率(kW)": 30.0,
    "引晶主功率标准差(kW)": 8.2
}
```

### 分析概况更新

新增"找到引晶数据文件数"统计项，显示成功提取引晶功率的文件数量。

## 使用方法

### 1. 运行分析
```bash
python run_analysis_fixed.py
```

### 2. 查看结果
- **详细结果**: `output_results/[文件夹名]_analysis.csv`
- **汇总结果**: `output_results/all_folders_summary.csv`
- **统计报告**: `output_results/analysis_report.txt`

### 3. 自定义配置
编辑 `config.json` 文件中的 `crystal_seeding_keyword` 字段来修改引晶关键词。

## 日志记录

系统会在 `crystal_analysis.log` 文件中记录：
- 引晶状态查找过程
- 功率数值提取结果
- 异常情况和错误信息

### 日志示例

```
2025-01-01 10:30:15 - INFO - 找到引晶时主功率: 45.2 kW
2025-01-01 10:30:16 - WARNING - 未找到包含'引晶'关键词的记录
2025-01-01 10:30:17 - ERROR - 查找引晶主功率时出错: 数据类型错误
```

## 技术实现

### 核心函数

```python
def find_first_crystal_seeding_power(self, df):
    """
    查找第一次出现"引晶"状态时的主功率数值
    
    Args:
        df (pd.DataFrame): 数据框
        
    Returns:
        float or None: 引晶时的主功率值(kW)，如果未找到则返回None
    """
```

### 实现特点

1. **配置驱动**: 通过配置文件灵活控制关键词
2. **异常安全**: 完善的异常处理机制
3. **数据验证**: 严格的数据类型和有效性检查
4. **日志记录**: 详细的操作日志便于调试
5. **向后兼容**: 不影响现有功能的正常运行

## 注意事项

1. **数据格式**: 确保CSV文件包含必需的 `cc_work_procedure` 和 `cc_main_heating_display` 字段
2. **编码问题**: 支持多种编码格式（UTF-8、GBK、GB2312）
3. **关键词匹配**: 关键词匹配区分大小写，请确保配置正确
4. **数据质量**: 建议在分析前检查原始数据的完整性

## 版本信息

- **版本**: v2.1.0
- **更新日期**: 2025-01-01
- **兼容性**: 向后兼容所有现有功能
- **依赖**: pandas, numpy, datetime, json, logging

## 技术支持

如遇到问题，请查看：
1. `crystal_analysis.log` 日志文件
2. 控制台输出信息
3. 配置文件格式是否正确
4. 原始数据是否包含必需字段
