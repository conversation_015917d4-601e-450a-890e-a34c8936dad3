gonglv:
  # 底加关一半、底加全关对应溶液比
  vice_close_ratio: [40, 50]

  # 几乎全熔的溶液比，根据半熔到当前时间判断有无超调风险，并调整主功率
  high_ratio: 90

  # 溶液比: 55->90，对应主功率调整时间标定, 10min-25min
  time_range: [10, 25]

  #降底加到关底加调整时间标定
  vice_time_range: [10,25]

  # 初始主加、底加
  init_power: [100, 80]

  # 接近全熔阶段的默认主加,接近全熔阶段的最低主加,全熔定锅位完成前的收尾主加,主加调整下限相对引晶功率的系数（setup时提供引晶功率时生效）
  done_power_k: [1.25, 0.7, 1.1, 0.55]
  # 接近全熔阶段的默认主加,接近全熔阶段的最低主加,全熔定锅位完成前的收尾主加，主加调整下限
  done_power: [90, 50, 80, 50]

  # 该时间之前不做调整，确保料筒提出对溶液比测定的影响完全消除，10min
  begin_t: 10

  # 几乎全熔之后，如果溶液比下降到该值，则判断主加过低有低温甚至结晶风险，重新将主加开至默认值
  undo_main_ratio: 87

  # 溶液比到该值时，判断已全熔，主加不再调整
  done_ratio: 98

  # 主功率拉低最长时间，30min
  down_time: 30

  # 首次底加全关之后，间隔该时间之后检查溶液比，判断是否需要重开底加，20min
  vice_check_time: 20

  # 底加重开最大时间, 15min
  max_reopen_time: 15

  # 全熔后，根据CCD3调整主功率的调整阶梯
  melting_ccd3: [1452, 1460]
  melting_power_k: [1.5,0.5]

  # 定锅位完成后，根据CCD调整主功率的调整阶梯
  key_ccd: [1444, 1446, 1448, 1452]
  key_power: [100, 95, 80, 60, 40]
  key_power_k: [1.5, 1.3, 1.3, 0.8, 0.5] # setup时提供引晶功率时生效

  #动态关底加，降主加开关
  dynamic_vice_heating: 0