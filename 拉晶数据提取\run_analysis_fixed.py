#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拉晶数据分析运行脚本 - 修复版本
解决权限问题和文件占用问题
"""

from crystal_data_analyzer import CrystalDataAnalyzer
import os
import time

def check_file_access(file_path):
    """检查文件是否可以写入"""
    try:
        # 尝试以写入模式打开文件
        with open(file_path, 'a', encoding='utf-8') as f:
            pass
        return True
    except PermissionError:
        return False
    except Exception:
        return False

def close_excel_files():
    """提示用户关闭Excel文件"""
    print("\n⚠️  检测到可能的文件权限问题")
    print("请确保:")
    print("1. 关闭所有打开的Excel文件")
    print("2. 关闭output_results文件夹中的所有CSV文件")
    print("3. 确保有足够的磁盘空间")
    print("\n按Enter键继续，或输入'q'退出...")
    
    user_input = input().strip().lower()
    if user_input == 'q':
        return False
    return True

def main():
    """主函数"""
    print("拉晶设备运行数据分析 - 修复版本")
    print("=" * 50)
    
    # 检查当前目录是否有数据文件夹
    current_dir = "."
    folders = [f for f in os.listdir(current_dir) 
               if os.path.isdir(f) and not f.startswith('.') and f != 'output_results']
    
    if not folders:
        print("错误：当前目录下没有找到数据文件夹")
        print("请确保将脚本放在包含设备运行数据文件夹的目录中")
        input("按Enter键退出...")
        return
    
    print(f"找到 {len(folders)} 个数据文件夹:")
    for folder in folders:
        csv_count = len([f for f in os.listdir(folder) if f.endswith('.csv')])
        print(f"  - {folder}: {csv_count} 个CSV文件")
    
    # 检查输出目录权限
    output_dir = "output_results"
    test_file = os.path.join(output_dir, "test_write_permission.tmp")
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        if not check_file_access(test_file):
            if not close_excel_files():
                return
    except Exception as e:
        print(f"无法创建输出目录: {e}")
        input("按Enter键退出...")
        return
    
    print("\n开始分析...")
    
    # 创建分析器并运行
    try:
        analyzer = CrystalDataAnalyzer()
        analyzer.run_analysis()
        
        print("\n✅ 分析完成！")
        print("结果文件保存在 'output_results' 文件夹中:")
        print("  - all_folders_summary.csv: 所有文件夹的汇总结果")
        print("  - [文件夹名]_analysis.csv: 各文件夹的详细结果")
        print("  - analysis_report.json: 统计报告(JSON格式)")
        print("  - analysis_report.txt: 统计报告(文本格式)")
        print("  - crystal_analysis.log: 分析日志")
        
    except PermissionError as e:
        print(f"\n❌ 权限错误: {e}")
        print("\n解决方案:")
        print("1. 关闭所有Excel程序")
        print("2. 关闭output_results文件夹中的所有文件")
        print("3. 以管理员身份运行PowerShell")
        print("4. 重新运行脚本")
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        print("请查看 crystal_analysis.log 文件获取详细错误信息")
    
    finally:
        # 清理临时文件
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
        except:
            pass
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
