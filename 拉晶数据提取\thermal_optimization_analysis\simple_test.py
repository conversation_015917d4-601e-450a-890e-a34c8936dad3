"""
简化测试版本 - 验证热量优化分析功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from thermal_risk_analyzer import ThermalRiskAnalyzer
from auxiliary_power_validator import AuxiliaryPowerValidator

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_thermal_optimization():
    """测试热量优化分析功能"""
    
    print("="*50)
    print("热量优化分析功能测试")
    print("="*50)
    
    # 1. 加载数据
    print("\n1. 加载数据...")
    try:
        df = pd.read_csv("../output_results/all_folders_summary.csv")
        print(f"成功加载 {len(df)} 条记录")
        print(f"数据列: {list(df.columns)}")
    except Exception as e:
        print(f"加载数据失败: {e}")
        return
    
    # 2. 初始化分析器
    print("\n2. 初始化分析器...")
    risk_analyzer = ThermalRiskAnalyzer()
    power_validator = AuxiliaryPowerValidator()
    
    # 3. 测试热量风险分析
    print("\n3. 测试热量风险分析...")
    sample_data = df.head(10)  # 只取前10条数据测试
    
    risk_results = []
    for idx, row in sample_data.iterrows():
        weight = row.get('weight_difference', 450)
        duration = row.get('duration_hours', 8)
        main_power = row.get('first_crystal_seeding_main_power_kw', 60)
        aux_power = row.get('vice_total_energy_kwh', 0) / duration if duration > 0 else 30
        
        # 计算热量积累风险
        risk = risk_analyzer.calculate_thermal_accumulation_risk(
            current_temp=1400,
            target_temp=1450,
            main_power=main_power,
            aux_power=aux_power,
            weight=weight,
            time_interval=0.1
        )
        
        # 计算动态阈值
        dynamic_threshold = risk_analyzer.calculate_solution_ratio_threshold(risk)
        
        risk_results.append({
            'file_index': idx,
            'weight': weight,
            'duration': duration,
            'main_power': main_power,
            'aux_power': aux_power,
            'thermal_risk': risk,
            'dynamic_threshold': dynamic_threshold
        })
    
    risk_df = pd.DataFrame(risk_results)
    print(f"热量风险分析完成，平均风险: {risk_df['thermal_risk'].mean():.3f}")
    
    # 4. 测试副加热功率验证
    print("\n4. 测试副加热功率验证...")
    validation_results = []
    
    for idx, row in sample_data.iterrows():
        main_power = row.get('main_total_energy_kwh', 0)
        aux_power = row.get('vice_total_energy_kwh', 0)
        weight = row.get('weight_difference', 0)
        duration = row.get('duration_hours', 0)
        
        if weight > 0 and duration > 0 and (main_power + aux_power) > 0:
            validation = power_validator.validate_auxiliary_power(
                actual_main_power=main_power,
                actual_aux_power=aux_power,
                weight=weight,
                duration=duration
            )
            
            validation_results.append({
                'file_index': idx,
                'weight': weight,
                'duration': duration,
                'main_power': main_power,
                'aux_power': aux_power,
                'total_energy': main_power + aux_power,
                'aux_ratio': validation['actual_aux_ratio'],
                'energy_efficiency': validation['energy_efficiency'],
                'reasonable': validation['aux_power_reasonable']
            })
    
    validation_df = pd.DataFrame(validation_results)
    if not validation_df.empty:
        print(f"副加热功率验证完成，合理率: {validation_df['reasonable'].mean():.1%}")
        print(f"平均副加热比例: {validation_df['aux_ratio'].mean():.3f}")
        print(f"平均能量效率: {validation_df['energy_efficiency'].mean():.3f}")
    
    # 5. 测试能量守恒验证
    print("\n5. 测试能量守恒验证...")
    conservation_results = []
    
    for idx, row in sample_data.iterrows():
        main_power = row.get('main_total_energy_kwh', 0)
        aux_power = row.get('vice_total_energy_kwh', 0)
        theoretical_energy = row.get('silicon_thermal_energy_kwh', 0)
        weight = row.get('weight_difference', 0)
        duration = row.get('duration_hours', 0)
        
        if weight > 0 and duration > 0:
            conservation = risk_analyzer.energy_conservation_verification(
                main_power_total=main_power,
                aux_power_total=aux_power,
                theoretical_energy=theoretical_energy,
                weight=weight,
                duration=duration
            )
            
            conservation_results.append({
                'file_index': idx,
                'total_input': conservation['total_input_energy_kwh'],
                'theoretical': conservation['theoretical_energy_kwh'],
                'efficiency': conservation['energy_efficiency'],
                'aux_ratio': conservation['aux_power_ratio'],
                'conservation_valid': conservation['energy_conservation_valid']
            })
    
    conservation_df = pd.DataFrame(conservation_results)
    if not conservation_df.empty:
        print(f"能量守恒验证完成，有效率: {conservation_df['conservation_valid'].mean():.1%}")
        print(f"平均能量效率: {conservation_df['efficiency'].mean():.3f}")
    
    # 6. 生成简单的可视化
    print("\n6. 生成测试图表...")
    try:
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('热量优化分析测试结果', fontsize=14, fontweight='bold')
        
        # 热量风险分布
        if not risk_df.empty:
            axes[0, 0].bar(range(len(risk_df)), risk_df['thermal_risk'])
            axes[0, 0].set_title('热量积累风险')
            axes[0, 0].set_ylabel('风险值')
            axes[0, 0].grid(True, alpha=0.3)
        
        # 动态阈值
        if not risk_df.empty:
            axes[0, 1].plot(risk_df['thermal_risk'], risk_df['dynamic_threshold'], 'o-')
            axes[0, 1].set_title('动态阈值调整')
            axes[0, 1].set_xlabel('热量风险')
            axes[0, 1].set_ylabel('动态阈值')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 副加热功率比例
        if not validation_df.empty:
            axes[1, 0].hist(validation_df['aux_ratio'], bins=5, alpha=0.7)
            axes[1, 0].axvline(0.35, color='red', linestyle='--', label='理论最优')
            axes[1, 0].set_title('副加热功率比例分布')
            axes[1, 0].set_xlabel('副加热比例')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # 能量效率
        if not conservation_df.empty:
            axes[1, 1].scatter(conservation_df['total_input'], conservation_df['efficiency'])
            axes[1, 1].set_title('能量效率分析')
            axes[1, 1].set_xlabel('总输入能量 (kWh)')
            axes[1, 1].set_ylabel('能量效率')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('thermal_optimization_test.png', dpi=300, bbox_inches='tight')
        print("测试图表已保存: thermal_optimization_test.png")
        plt.show()
        
    except Exception as e:
        print(f"生成图表时出错: {e}")
    
    # 7. 生成测试报告
    print("\n7. 生成测试报告...")
    report = f"""
热量优化分析功能测试报告
==================================================
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试样本数: {len(sample_data)}

热量风险分析结果:
------------------------------
平均热量风险: {risk_df['thermal_risk'].mean():.3f}
最大热量风险: {risk_df['thermal_risk'].max():.3f}
最小热量风险: {risk_df['thermal_risk'].min():.3f}
动态阈值范围: {risk_df['dynamic_threshold'].min():.3f} - {risk_df['dynamic_threshold'].max():.3f}

副加热功率验证结果:
------------------------------
"""
    
    if not validation_df.empty:
        report += f"""验证样本数: {len(validation_df)}
副加热功率合理率: {validation_df['reasonable'].mean():.1%}
平均副加热比例: {validation_df['aux_ratio'].mean():.3f}
平均能量效率: {validation_df['energy_efficiency'].mean():.3f}
"""
    
    if not conservation_df.empty:
        report += f"""
能量守恒验证结果:
------------------------------
验证样本数: {len(conservation_df)}
能量守恒有效率: {conservation_df['conservation_valid'].mean():.1%}
平均能量效率: {conservation_df['efficiency'].mean():.3f}
"""
    
    report += f"""
测试结论:
------------------------------
1. 热量风险分析功能正常，能够计算风险值并动态调整阈值
2. 副加热功率验证功能正常，能够评估功率配比合理性
3. 能量守恒验证功能正常，能够分析能量平衡情况
4. 所有核心算法模块运行正常，可以进行完整分析

优化建议:
------------------------------
1. 建议将副加热功率比例控制在20%-60%范围内
2. 根据热量积累风险动态调整控制策略
3. 持续监控能量效率，目标范围25%-35%
4. 实施基于物理模型的预测控制
"""
    
    # 保存测试报告
    with open('thermal_optimization_test_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("测试报告已保存: thermal_optimization_test_report.txt")
    print("\n" + "="*50)
    print("热量优化分析功能测试完成！")
    print("="*50)

if __name__ == "__main__":
    test_thermal_optimization()
