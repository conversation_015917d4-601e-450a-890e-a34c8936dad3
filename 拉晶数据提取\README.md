# 拉晶设备运行数据分析脚本

## 功能概述

本脚本用于分析拉晶设备运行数据，能够自动处理多个文件夹中的CSV文件，提取关键运行参数并计算总能量消耗。

### 🆕 新增功能：首投/复投智能分析

- **自动分类**：根据 `cc_feed_number` 字段自动识别首投/复投类型
- **智能筛选**：支持按首投/复投类型筛选分析结果
- **对比分析**：提供首投与复投的详细对比统计
- **交互界面**：全新的用户友好交互式操作界面

## 主要功能

1. **数据提取**：
   - 运行开始时间：第一条记录的`cc_time`字段值
   - 运行结束时间：在`cc_work_procedure`字段中找到"预调温"状态对应的`cc_time`，然后加上10分钟
   - 开始重量：运行开始时间对应的`cc_residue_weight`值
   - 结束重量：运行结束时间对应的`cc_residue_weight`值
   - 重量差值：结束重量 - 开始重量

2. **能量计算**：
   - 主功率总能量：根据`cc_main_heating_display`字段值和时间间隔计算总输入能量
   - 副功率总能量：根据`cc_vice_heating_set`字段值和时间间隔计算总输入能量

3. **首投/复投分析**：
   - 根据 `cc_feed_number` 字段自动判断首投/复投类型
   - 判断逻辑：如果文件中有任何记录的 `cc_feed_number` 等于1，则为首投，否则为复投
   - 支持按类型筛选和统计分析

4. **输出结果**：
   - 每个文件夹的所有CSV文件处理结果合并为一个CSV文件
   - 所有文件夹生成的汇总CSV文件统一存放到输出文件夹中
   - 生成统计报告和分析日志
   - 新增首投/复投统计信息

## 文件结构

```
项目目录/
├── crystal_data_analyzer.py    # 主分析脚本
├── run_analysis.py            # 简化运行脚本
├── interactive_feed_analyzer.py # 🆕 交互式首投/复投分析器
├── run_analysis_with_feed_filter.py # 🆕 带筛选的分析脚本
├── feed_type_classifier.py    # 🆕 首投/复投分类器
├── test_feed_functionality.py # 🆕 功能测试脚本
├── config.json               # 配置文件
├── README.md                 # 说明文档
├── 首投复投功能使用指南.md      # 🆕 新功能使用指南
├── analoga01/               # 设备运行数据文件夹1
│   ├── extracted_content_1.csv
│   ├── extracted_content_2.csv
│   └── ...
├── analoga02/               # 设备运行数据文件夹2
│   ├── extracted_content_1.csv
│   └── ...
└── output_results/          # 输出结果文件夹（自动创建）
    ├── all_folders_summary.csv
    ├── analoga01_analysis.csv
    ├── analoga02_analysis.csv
    ├── analysis_report.json
    ├── analysis_report.txt
    └── crystal_analysis.log
```

## 使用方法

### 🆕 方法1：使用交互式首投/复投分析器（强烈推荐）

```bash
python interactive_feed_analyzer.py
```

提供用户友好的交互界面，支持：
- 选择分析类型（全部/首投/复投/对比）
- 实时查看分析结果和统计信息
- 详细的首投/复投对比分析

### 🆕 方法2：使用筛选脚本

```bash
python run_analysis_with_feed_filter.py
```

支持命令行交互选择筛选条件。

### 方法3：使用简化脚本

```bash
python run_analysis.py
```

### 方法4：直接运行主脚本

```bash
python crystal_data_analyzer.py
```

### 方法5：在Python代码中使用

```python
from crystal_data_analyzer import CrystalDataAnalyzer

analyzer = CrystalDataAnalyzer()
analyzer.run_analysis()
```

## 配置说明

配置文件 `config.json` 包含以下参数：

```json
{
    "input_directory": ".",                    // 输入目录
    "output_directory": "output_results",     // 输出目录
    "time_format": "%Y-%m-%d %H:%M:%S",      // 时间格式
    "preheating_duration_minutes": 10,        // 预调温持续时间（分钟）
    "required_columns": [                      // 必需的CSV列
        "cc_time",
        "cc_work_procedure",
        "cc_residue_weight",
        "cc_main_heating_display",
        "cc_vice_heating_set",
        "cc_ccd_liquid_temperature"
    ],
    "target_procedure": "预调温",              // 目标工艺状态
    "encoding": "utf-8",                      // 文件编码
    "feed_type_analysis": {                   // 🆕 首投/复投分析配置
        "enabled": true,                      // 是否启用首投/复投分析
        "feed_number_column": "cc_feed_number", // 字段名称
        "filter_type": "全部"                 // 筛选类型: "首投"/"复投"/"全部"
    }
}
```

## 输出文件说明

### 1. 汇总结果文件 (all_folders_summary.csv)

包含所有文件夹中所有CSV文件的分析结果：

| 列名 | 说明 |
|------|------|
| file_path | 文件完整路径 |
| file_name | 文件名 |
| folder_name | 文件夹名 |
| start_time | 运行开始时间 |
| end_time | 运行结束时间 |
| start_weight | 开始重量 |
| end_weight | 结束重量 |
| weight_difference | 重量差值 |
| end_temperature_celsius | 结束时CCD液体温度(°C) |
| main_total_energy_kwh | 主功率总能量(kWh) |
| vice_total_energy_kwh | 副功率总能量(kWh) |
| total_energy_kwh | 总能量(kWh) |
| silicon_thermal_energy_kwh | 硅理论热能需求(kWh) |
| energy_efficiency_percent | 能量效率(%) |
| feed_type | 🆕 首投/复投类型 |
| cc_feed_number_values | 🆕 cc_feed_number字段唯一值 |
| feed_number_1_records | 🆕 等于1的记录数量 |
| record_count | 记录数量 |
| duration_hours | 运行时长(小时) |
| preheating_found | 是否找到预调温状态 |

### 2. 单文件夹结果文件 ([文件夹名]_analysis.csv)

每个文件夹的详细分析结果，格式与汇总文件相同。

### 3. 统计报告 (analysis_report.json/txt)

包含以下统计信息：
- 分析概况（总文件数、成功分析文件数等）
- 🆕 首投/复投统计（文件数量、比例、能效对比等）
- 重量统计（平均值、标准差等）
- 能量统计（平均值、标准差等）
- 时间统计（平均运行时长、最短/最长时长等）

### 4. 分析日志 (crystal_analysis.log)

记录分析过程中的详细信息、警告和错误。

## 依赖环境

### Python版本
- Python 3.6 或更高版本

### 必需的Python包
```bash
pip install pandas numpy
```

### 完整安装命令
```bash
# 如果使用conda
conda install pandas numpy

# 如果使用pip
pip install pandas numpy
```

## 错误处理

脚本具有完善的错误处理机制：

1. **编码问题**：自动尝试多种编码格式（utf-8, gbk, gb2312, utf-8-sig）
2. **缺失列**：检查必需列是否存在，记录警告信息
3. **时间解析**：支持多种时间格式的自动识别
4. **数据验证**：验证数据完整性和有效性
5. **异常捕获**：捕获并记录所有处理异常

## 注意事项

1. **数据格式**：确保CSV文件包含必需的列名
2. **文件编码**：支持多种中文编码，但建议使用UTF-8
3. **时间格式**：默认支持"YYYY-MM-DD HH:MM:SS"格式
4. **内存使用**：大文件处理时注意内存使用情况
5. **权限问题**：确保脚本有读取输入文件和写入输出文件的权限

## 自定义配置

可以通过修改 `config.json` 文件来自定义分析参数：

- 修改输入/输出目录
- 调整预调温持续时间
- 更改目标工艺状态名称
- 添加或删除必需列
- 修改时间格式

## 🆕 首投/复投功能快速入门

### 判断逻辑
- **首投**：文件中 `cc_feed_number` 字段有任何记录等于1
- **复投**：文件中 `cc_feed_number` 字段没有任何记录等于1

### 快速开始
```bash
# 使用交互式分析器（推荐）
python interactive_feed_analyzer.py

# 运行功能测试
python test_feed_functionality.py
```

### 详细文档
请参阅 `首投复投功能使用指南.md` 获取完整的使用说明和示例。

## 技术支持

如遇到问题，请检查：
1. 分析日志文件 `crystal_analysis.log`
2. 确认数据文件格式是否正确
3. 检查配置文件设置是否合适
4. 验证Python环境和依赖包是否正确安装
5. 🆕 运行 `python test_feed_functionality.py` 测试新功能
