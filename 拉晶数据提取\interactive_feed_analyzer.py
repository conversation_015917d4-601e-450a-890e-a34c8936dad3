#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式首投/复投分析器
Interactive Feed Type Analyzer

功能：
1. 提供用户友好的交互界面
2. 支持实时选择筛选条件
3. 显示详细的分析结果和统计信息
4. 支持结果导出和查看

作者：AI Assistant
日期：2025-01-07
"""

import os
import sys
import json
import pandas as pd
from crystal_data_analyzer import CrystalDataAnalyzer


class InteractiveFeedAnalyzer:
    """交互式首投/复投分析器"""
    
    def __init__(self):
        self.analyzer = None
        self.results = []
        
    def show_welcome(self):
        """显示欢迎界面"""
        print("\n" + "="*60)
        print("🔬 拉晶设备运行数据分析 - 首投/复投智能分析系统")
        print("="*60)
        print("\n功能特点:")
        print("✓ 智能识别首投/复投类型")
        print("✓ 支持多种筛选条件")
        print("✓ 生成详细统计报告")
        print("✓ 交互式操作界面")
        print("\n判断逻辑:")
        print("• 如果文件中cc_feed_number字段有任何记录等于1 → 首投")
        print("• 否则 → 复投")
        
    def get_filter_choice(self):
        """获取筛选选择"""
        print("\n" + "-"*40)
        print("📊 请选择分析类型:")
        print("-"*40)
        print("1. 🔍 全部数据分析（首投+复投）")
        print("2. 🥇 仅分析首投数据")
        print("3. 🔄 仅分析复投数据")
        print("4. 📈 对比分析（分别统计首投和复投）")
        print("5. ❌ 退出程序")
        
        while True:
            try:
                choice = input("\n请输入选择 (1-5): ").strip()
                if choice in ["1", "2", "3", "4", "5"]:
                    return choice
                else:
                    print("❌ 无效选择，请输入1-5之间的数字。")
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                sys.exit(0)
            except Exception as e:
                print(f"❌ 输入错误: {e}")
    
    def create_config(self, filter_type="全部"):
        """创建分析配置"""
        config = {
            "input_directory": ".",
            "output_directory": "interactive_analysis_results",
            "time_format": "%Y-%m-%d %H:%M:%S",
            "preheating_duration_minutes": 10,
            "required_columns": [
                "cc_time",
                "cc_work_procedure",
                "cc_residue_weight",
                "cc_main_heating_display",
                "cc_vice_heating_set",
                "cc_ccd_liquid_temperature"
            ],
            "target_procedure": "预调温",
            "crystal_seeding_keyword": "引晶",
            "encoding": "utf-8",
            "feed_type_analysis": {
                "enabled": True,
                "feed_number_column": "cc_feed_number",
                "filter_type": filter_type
            }
        }
        return config
    
    def run_analysis(self, filter_type):
        """运行分析"""
        print(f"\n🚀 开始分析 - 筛选类型: {filter_type}")
        print("⏳ 正在处理数据，请稍候...")
        
        # 创建配置
        config = self.create_config(filter_type)
        
        # 创建临时配置文件
        config_file = "temp_interactive_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        
        try:
            # 创建分析器并运行
            self.analyzer = CrystalDataAnalyzer(config_file)
            self.analyzer.run_analysis()
            
            # 读取结果
            output_dir = config['output_directory']
            summary_file = os.path.join(output_dir, "all_folders_summary.csv")
            
            if os.path.exists(summary_file):
                self.results = pd.read_csv(summary_file, encoding='utf-8').to_dict('records')
                return True
            else:
                print("❌ 未找到分析结果文件")
                return False
                
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            return False
        finally:
            # 清理临时文件
            try:
                os.remove(config_file)
            except:
                pass
    
    def show_summary_statistics(self):
        """显示汇总统计"""
        if not self.results:
            print("❌ 没有分析结果")
            return
        
        print("\n" + "="*50)
        print("📊 分析结果汇总")
        print("="*50)
        
        total_files = len(self.results)
        first_feed_files = len([r for r in self.results if r.get('feed_type') == '首投'])
        repeat_feed_files = len([r for r in self.results if r.get('feed_type') == '复投'])
        unknown_files = total_files - first_feed_files - repeat_feed_files
        
        print(f"\n📁 文件统计:")
        print(f"  • 总文件数: {total_files}")
        print(f"  • 首投文件: {first_feed_files} ({first_feed_files/total_files*100:.1f}%)")
        print(f"  • 复投文件: {repeat_feed_files} ({repeat_feed_files/total_files*100:.1f}%)")
        if unknown_files > 0:
            print(f"  • 未知类型: {unknown_files}")
        
        # 能效统计
        if self.results:
            efficiencies = [r.get('energy_efficiency_percent', 0) for r in self.results if r.get('energy_efficiency_percent', 0) > 0]
            if efficiencies:
                avg_efficiency = sum(efficiencies) / len(efficiencies)
                print(f"\n⚡ 能效统计:")
                print(f"  • 平均能效: {avg_efficiency:.2f}%")
                print(f"  • 最高能效: {max(efficiencies):.2f}%")
                print(f"  • 最低能效: {min(efficiencies):.2f}%")
        
        # 首投vs复投对比
        if first_feed_files > 0 and repeat_feed_files > 0:
            first_feed_results = [r for r in self.results if r.get('feed_type') == '首投']
            repeat_feed_results = [r for r in self.results if r.get('feed_type') == '复投']
            
            first_efficiencies = [r.get('energy_efficiency_percent', 0) for r in first_feed_results if r.get('energy_efficiency_percent', 0) > 0]
            repeat_efficiencies = [r.get('energy_efficiency_percent', 0) for r in repeat_feed_results if r.get('energy_efficiency_percent', 0) > 0]
            
            if first_efficiencies and repeat_efficiencies:
                first_avg = sum(first_efficiencies) / len(first_efficiencies)
                repeat_avg = sum(repeat_efficiencies) / len(repeat_efficiencies)
                
                print(f"\n🔄 首投vs复投对比:")
                print(f"  • 首投平均能效: {first_avg:.2f}%")
                print(f"  • 复投平均能效: {repeat_avg:.2f}%")
                print(f"  • 能效差异: {first_avg - repeat_avg:+.2f}%")
    
    def show_detailed_results(self, feed_type_filter=None):
        """显示详细结果"""
        if not self.results:
            print("❌ 没有分析结果")
            return
        
        filtered_results = self.results
        if feed_type_filter:
            filtered_results = [r for r in self.results if r.get('feed_type') == feed_type_filter]
        
        if not filtered_results:
            print(f"❌ 没有找到{feed_type_filter}类型的结果")
            return
        
        print(f"\n📋 详细结果 ({len(filtered_results)}个文件):")
        print("-" * 80)
        
        for i, result in enumerate(filtered_results[:10], 1):  # 只显示前10个
            folder_name = result.get('folder_name', 'Unknown')
            feed_type = result.get('feed_type', 'Unknown')
            efficiency = result.get('energy_efficiency_percent', 0)
            feed_values = result.get('cc_feed_number_values', '[]')
            feed_1_records = result.get('feed_number_1_records', 0)
            
            print(f"{i:2d}. {folder_name} | {feed_type} | 能效:{efficiency:.1f}% | cc_feed_number:{feed_values} | 等于1的记录:{feed_1_records}")
        
        if len(filtered_results) > 10:
            print(f"... 还有 {len(filtered_results) - 10} 个文件")
    
    def show_results_menu(self):
        """显示结果查看菜单"""
        while True:
            print("\n" + "-"*40)
            print("📊 结果查看选项:")
            print("-"*40)
            print("1. 📈 查看汇总统计")
            print("2. 📋 查看全部详细结果")
            print("3. 🥇 查看首投详细结果")
            print("4. 🔄 查看复投详细结果")
            print("5. 📁 打开结果文件夹")
            print("6. 🔙 返回主菜单")
            
            try:
                choice = input("\n请选择 (1-6): ").strip()
                
                if choice == "1":
                    self.show_summary_statistics()
                elif choice == "2":
                    self.show_detailed_results()
                elif choice == "3":
                    self.show_detailed_results("首投")
                elif choice == "4":
                    self.show_detailed_results("复投")
                elif choice == "5":
                    output_dir = "interactive_analysis_results"
                    if os.path.exists(output_dir):
                        os.startfile(output_dir)  # Windows
                        print(f"✅ 已打开结果文件夹: {output_dir}")
                    else:
                        print("❌ 结果文件夹不存在")
                elif choice == "6":
                    break
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n👋 返回主菜单")
                break
            except Exception as e:
                print(f"❌ 操作错误: {e}")
    
    def run_comparative_analysis(self):
        """运行对比分析"""
        print("\n🔄 开始对比分析...")
        
        # 先运行全部数据分析
        if not self.run_analysis("全部"):
            return
        
        self.show_summary_statistics()
        
        # 生成对比报告
        if self.results:
            first_feed_results = [r for r in self.results if r.get('feed_type') == '首投']
            repeat_feed_results = [r for r in self.results if r.get('feed_type') == '复投']
            
            print(f"\n📊 对比分析报告:")
            print(f"  首投文件数: {len(first_feed_results)}")
            print(f"  复投文件数: {len(repeat_feed_results)}")
            
            if first_feed_results and repeat_feed_results:
                # 能效对比
                first_efficiencies = [r.get('energy_efficiency_percent', 0) for r in first_feed_results if r.get('energy_efficiency_percent', 0) > 0]
                repeat_efficiencies = [r.get('energy_efficiency_percent', 0) for r in repeat_feed_results if r.get('energy_efficiency_percent', 0) > 0]
                
                if first_efficiencies and repeat_efficiencies:
                    first_avg = sum(first_efficiencies) / len(first_efficiencies)
                    repeat_avg = sum(repeat_efficiencies) / len(repeat_efficiencies)
                    
                    print(f"\n⚡ 能效对比:")
                    print(f"  首投平均能效: {first_avg:.2f}%")
                    print(f"  复投平均能效: {repeat_avg:.2f}%")
                    
                    if first_avg > repeat_avg:
                        print(f"  🏆 首投能效更高，高出 {first_avg - repeat_avg:.2f}%")
                    elif repeat_avg > first_avg:
                        print(f"  🏆 复投能效更高，高出 {repeat_avg - first_avg:.2f}%")
                    else:
                        print(f"  ⚖️ 首投和复投能效相当")
    
    def run(self):
        """运行交互式分析器"""
        self.show_welcome()
        
        while True:
            choice = self.get_filter_choice()
            
            if choice == "1":
                if self.run_analysis("全部"):
                    self.show_results_menu()
            elif choice == "2":
                if self.run_analysis("首投"):
                    self.show_results_menu()
            elif choice == "3":
                if self.run_analysis("复投"):
                    self.show_results_menu()
            elif choice == "4":
                self.run_comparative_analysis()
                self.show_results_menu()
            elif choice == "5":
                print("\n👋 感谢使用，再见！")
                break


def main():
    """主函数"""
    try:
        analyzer = InteractiveFeedAnalyzer()
        analyzer.run()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("请检查数据文件和运行环境")


if __name__ == "__main__":
    main()
