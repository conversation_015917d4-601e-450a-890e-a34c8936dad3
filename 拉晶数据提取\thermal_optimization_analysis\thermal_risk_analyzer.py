"""
热量积累风险分析器
基于能量守恒原理的动态风险评估系统
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Tuple, Dict, List
import warnings
warnings.filterwarnings('ignore')

class ThermalRiskAnalyzer:
    """热量积累风险分析器"""
    
    def __init__(self):
        # 硅的物理常数
        self.silicon_density = 2330  # kg/m³
        self.silicon_specific_heat = 0.7  # kJ/(kg·K)
        self.silicon_melting_point = 1414  # °C
        self.silicon_latent_heat = 1800  # kJ/kg (熔化潜热)
        
        # 系统参数
        self.thermal_efficiency = 0.28  # 热效率
        self.heat_loss_coefficient = 0.1  # 散热系数
        self.time_constant = 3600  # 时间常数(秒)
        
    def calculate_thermal_accumulation_risk(self, 
                                          current_temp: float,
                                          target_temp: float,
                                          main_power: float,
                                          aux_power: float,
                                          weight: float,
                                          time_interval: float) -> float:
        """
        计算热量积累风险
        
        Args:
            current_temp: 当前温度 (°C)
            target_temp: 目标温度 (°C)
            main_power: 主加热功率 (kW)
            aux_power: 副加热功率 (kW)
            weight: 硅重量 (kg)
            time_interval: 时间间隔 (小时)
            
        Returns:
            风险值 [0,1]
        """
        # 计算输入功率
        total_input_power = main_power + aux_power  # kW
        
        # 计算功率损失 (dE/dt = P_input - P_loss)
        power_loss = self.calculate_power_loss(current_temp, weight)
        
        # 热量输入计算：功率强度归一化
        # 当前总功率相对于引晶功率进行归一化，引晶功率作为基准
        heat_input = (total_input_power * time_interval) / (self.thermal_efficiency * 100)
        
        # 热量损失计算：H(t+ Δt) = H(t) × λ(t) + Q_input(Δt)
        # 其中因子：λ(t) = e^(-Δt/τ)
        lambda_factor = np.exp(-time_interval / self.time_constant)
        heat_loss = power_loss * lambda_factor * time_interval
        
        # 归一化散热能量守恒方程：E(t+ Δt) = E(t) × e^(-Δt/τ) + P_input × Δt/C_eff
        # 其中第一项：E(t) × e^(-Δt/τ) 表示考虑散热后剩余的能量
        # 第二项：P_input × Δt/C_eff 表示新输入的能量
        effective_capacity = weight * self.silicon_specific_heat  # kJ/K
        
        accumulated_energy = heat_input - heat_loss
        normalized_energy = accumulated_energy / effective_capacity
        
        # 温度偏差风险
        temp_deviation = abs(current_temp - target_temp) / target_temp
        
        # 综合风险评估
        risk = min(1.0, max(0.0, 
                           0.6 * normalized_energy + 
                           0.4 * temp_deviation))
        
        return risk
    
    def calculate_power_loss(self, temperature: float, weight: float) -> float:
        """计算功率损失"""
        # 基于温度和重量的散热损失模型
        surface_area = (weight / self.silicon_density) ** (2/3)  # 近似表面积
        temp_kelvin = temperature + 273.15
        
        # Stefan-Boltzmann辐射损失 + 对流损失
        radiation_loss = 5.67e-8 * surface_area * (temp_kelvin**4 - 293.15**4) / 1000  # kW
        convection_loss = self.heat_loss_coefficient * surface_area * (temperature - 20) / 1000  # kW
        
        return radiation_loss + convection_loss
    
    def calculate_solution_ratio_threshold(self, risk: float, base_threshold: float = 0.8) -> float:
        """
        动态调整关闭底加热的溶液比阈值
        
        Args:
            risk: 热量积累风险 [0,1]
            base_threshold: 基础阈值
            
        Returns:
            调整后的阈值
        """
        # 风险越高，越早关闭底加热（降低阈值）
        # 风险越低，越晚关闭底加热（提高阈值）
        adjustment_factor = 1 - 0.3 * risk  # 最大调整30%
        adjusted_threshold = base_threshold * adjustment_factor
        
        return max(0.5, min(0.95, adjusted_threshold))  # 限制在合理范围内
    
    def energy_conservation_verification(self, 
                                       main_power_total: float,
                                       aux_power_total: float,
                                       theoretical_energy: float,
                                       weight: float,
                                       duration: float) -> Dict:
        """
        通过能量守恒方法验证副加热总功率和实际对比
        
        Args:
            main_power_total: 主加热总能量 (kWh)
            aux_power_total: 副加热总能量 (kWh)
            theoretical_energy: 理论热能需求 (kWh)
            weight: 重量 (kg)
            duration: 持续时间 (小时)
            
        Returns:
            验证结果字典
        """
        total_input_energy = main_power_total + aux_power_total
        
        # 计算理论最小能量需求（从室温到熔点 + 熔化潜热）
        sensible_heat = weight * self.silicon_specific_heat * (self.silicon_melting_point - 20) / 3600  # kWh
        latent_heat = weight * self.silicon_latent_heat / 3600  # kWh
        min_theoretical_energy = sensible_heat + latent_heat
        
        # 计算散热损失
        avg_temp = (20 + self.silicon_melting_point) / 2
        avg_power_loss = self.calculate_power_loss(avg_temp, weight)
        heat_loss_energy = avg_power_loss * duration  # kWh
        
        # 能量平衡分析
        useful_energy = total_input_energy - heat_loss_energy
        energy_efficiency = useful_energy / total_input_energy if total_input_energy > 0 else 0
        
        # 副加热功率合理性分析
        aux_power_ratio = aux_power_total / total_input_energy if total_input_energy > 0 else 0
        main_power_ratio = main_power_total / total_input_energy if total_input_energy > 0 else 0
        
        # 能量守恒验证
        energy_balance = abs(useful_energy - theoretical_energy) / theoretical_energy if theoretical_energy > 0 else float('inf')
        
        verification_result = {
            'total_input_energy_kwh': total_input_energy,
            'theoretical_energy_kwh': theoretical_energy,
            'min_theoretical_energy_kwh': min_theoretical_energy,
            'heat_loss_energy_kwh': heat_loss_energy,
            'useful_energy_kwh': useful_energy,
            'energy_efficiency': energy_efficiency,
            'aux_power_ratio': aux_power_ratio,
            'main_power_ratio': main_power_ratio,
            'energy_balance_error': energy_balance,
            'aux_power_reasonable': 0.2 <= aux_power_ratio <= 0.6,  # 副加热占比合理范围
            'energy_conservation_valid': energy_balance < 0.2,  # 能量平衡误差小于20%
            'overall_efficiency_reasonable': 0.15 <= energy_efficiency <= 0.35  # 整体效率合理范围
        }
        
        return verification_result
    
    def analyze_thermal_optimization(self, df: pd.DataFrame) -> Dict:
        """
        分析热量优化方案的效果
        
        Args:
            df: 包含温度、功率、重量等数据的DataFrame
            
        Returns:
            优化分析结果
        """
        results = []
        
        for idx, row in df.iterrows():
            if idx == 0:
                continue
                
            # 计算时间间隔
            time_interval = 1/60  # 假设1分钟间隔，转换为小时
            
            # 计算热量积累风险
            risk = self.calculate_thermal_accumulation_risk(
                current_temp=row.get('cc_ccd_liquid_temperature', 1400),
                target_temp=1450,  # 假设目标温度
                main_power=row.get('cc_main_heating_display', 60),
                aux_power=row.get('cc_aux_heating_display', 30),
                weight=row.get('weight_diff', 450),
                time_interval=time_interval
            )
            
            # 计算动态阈值
            dynamic_threshold = self.calculate_solution_ratio_threshold(risk)
            
            results.append({
                'timestamp': idx,
                'thermal_risk': risk,
                'dynamic_threshold': dynamic_threshold,
                'temperature': row.get('cc_ccd_liquid_temperature', 1400),
                'main_power': row.get('cc_main_heating_display', 60),
                'aux_power': row.get('cc_aux_heating_display', 30)
            })
        
        return {
            'risk_analysis': pd.DataFrame(results),
            'avg_risk': np.mean([r['thermal_risk'] for r in results]),
            'max_risk': np.max([r['thermal_risk'] for r in results]),
            'threshold_range': (
                np.min([r['dynamic_threshold'] for r in results]),
                np.max([r['dynamic_threshold'] for r in results])
            )
        }
